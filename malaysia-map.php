<?php
session_start();

// Simple config without database dependencies
if (!defined('SUPPORTED_LANGS')) {
    define('SUPPORTED_LANGS', ['en', 'ar']);
}
if (!defined('DEFAULT_LANG')) {
    define('DEFAULT_LANG', 'en');
}
if (!defined('WHATSAPP_NUMBER')) {
    define('WHATSAPP_NUMBER', '+60123456789');
}

// Handle language switching
if (isset($_GET['lang']) && in_array($_GET['lang'], SUPPORTED_LANGS)) {
    $_SESSION['lang'] = $_GET['lang'];
}

// Get current language
$lang = $_SESSION['lang'] ?? DEFAULT_LANG;
$isRTL = $lang === 'ar';

// Simple date formatting function
function formatDate($date, $lang = 'en') {
    return date('M j, Y', strtotime($date));
}
?>

<!DOCTYPE html>
<html lang="<?php echo $lang; ?>" dir="<?php echo $isRTL ? 'rtl' : 'ltr'; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $lang === 'ar' ? 'خريطة ماليزيا التفاعلية - رحلات ماليزيا السياحية' : 'Interactive Malaysia Map - Malaysia Tourism Trips'; ?></title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <?php if ($lang === 'ar'): ?>
        <link href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <?php else: ?>
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <?php endif; ?>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'arabic': ['IBM Plex Sans Arabic', 'sans-serif'],
                        'english': ['Inter', 'sans-serif']
                    },
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1'
                        },
                        secondary: {
                            500: '#f59e0b',
                            600: '#d97706'
                        }
                    }
                }
            }
        }
    </script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    
    <!-- IMMEDIATE FIX: Force content visibility -->
    <style>
        .scroll-animate,
        .article-card,
        .destination-card,
        .gallery-item {
            opacity: 1 !important;
            transform: translateY(0) !important;
            visibility: visible !important;
        }
        
        /* Map Styles */
        .malaysia-map {
            max-width: 100%;
            height: auto;
        }
        
        .map-state {
            fill: #e5e7eb;
            stroke: #ffffff;
            stroke-width: 2;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .map-state:hover {
            fill: #0ea5e9;
            stroke: #0284c7;
            stroke-width: 3;
        }
        
        .map-state.active {
            fill: #0284c7;
            stroke: #0369a1;
            stroke-width: 3;
        }
        
        .state-info-panel {
            transform: translateX(100%);
            transition: transform 0.3s ease;
        }
        
        .state-info-panel.active {
            transform: translateX(0);
        }
        
        .tourist-attraction {
            transition: all 0.2s ease;
        }
        
        .tourist-attraction:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
    </style>
    
    <!-- GSAP for animations -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js"></script>
</head>
<body class="<?php echo $lang === 'ar' ? 'font-arabic' : 'font-english'; ?> bg-gray-50">
    
    <?php
    if (file_exists('includes/header.php')) {
        include 'includes/header.php';
    } else {
        // Simple header fallback
        echo '<header class="fixed top-0 left-0 right-0 bg-white shadow-md z-50 h-16"></header>';
    }
    ?>
    
    <!-- Simple Navigation -->
    <nav class="bg-white shadow-md py-4 mt-16">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between items-center">
                <div class="flex items-center space-x-4">
                    <h1 class="text-xl font-bold text-gray-800">
                        <?php echo $lang === 'ar' ? 'رحلات ماليزيا السياحية' : 'Malaysia Tourism Trips'; ?>
                    </h1>
                </div>

                <div class="flex items-center space-x-4">
                    <a href="index.php?lang=<?php echo $lang; ?>" class="text-gray-600 hover:text-blue-600">
                        <?php echo $lang === 'ar' ? 'الرئيسية' : 'Home'; ?>
                    </a>
                    <a href="about.php?lang=<?php echo $lang; ?>" class="text-gray-600 hover:text-blue-600">
                        <?php echo $lang === 'ar' ? 'من نحن' : 'About'; ?>
                    </a>
                    <a href="blog.php?lang=<?php echo $lang; ?>" class="text-gray-600 hover:text-blue-600">
                        <?php echo $lang === 'ar' ? 'المدونة' : 'Blog'; ?>
                    </a>
                    <a href="contact.php?lang=<?php echo $lang; ?>" class="text-gray-600 hover:text-blue-600">
                        <?php echo $lang === 'ar' ? 'اتصل بنا' : 'Contact'; ?>
                    </a>

                    <!-- Language Switch -->
                    <div class="flex gap-2">
                        <a href="?lang=en" class="px-3 py-1 rounded <?php echo $lang === 'en' ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700'; ?>">
                            EN
                        </a>
                        <a href="?lang=ar" class="px-3 py-1 rounded <?php echo $lang === 'ar' ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700'; ?>">
                            AR
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <main>
        <!-- Hero Section -->
        <section class="relative h-96 bg-gradient-to-r from-primary-600 to-primary-800 overflow-hidden">
            <div class="absolute inset-0 bg-black bg-opacity-30"></div>
            <div class="relative z-10 flex items-center justify-center h-full text-white text-center">
                <div class="max-w-4xl mx-auto px-4">
                    <h1 class="text-5xl md:text-6xl font-bold mb-4 hero-title">
                        <?php echo $lang === 'ar' ? 'خريطة ماليزيا التفاعلية' : 'Interactive Malaysia Map'; ?>
                    </h1>
                    <p class="text-xl md:text-2xl hero-subtitle">
                        <?php echo $lang === 'ar' ? 'اكتشف أجمل الوجهات السياحية في كل ولاية' : 'Discover the most beautiful tourist destinations in each state'; ?>
                    </p>
                </div>
            </div>
        </section>

        <!-- Map Section -->
        <section class="py-12 bg-white">
            <div class="max-w-7xl mx-auto px-4">
                <div class="text-center mb-8 scroll-animate">
                    <h2 class="text-3xl font-bold mb-4 text-gray-800">
                        <?php echo $lang === 'ar' ? 'انقر على أي ولاية لاستكشاف معالمها' : 'Click on any state to explore its attractions'; ?>
                    </h2>
                    <p class="text-lg text-gray-600">
                        <?php echo $lang === 'ar' ? 'اختر ولاية من الخريطة لمعرفة أهم المعالم السياحية فيها' : 'Select a state from the map to learn about its top tourist attractions'; ?>
                    </p>
                </div>
                
                <div class="grid lg:grid-cols-3 gap-8">
                    <!-- Map Container -->
                    <div class="lg:col-span-2">
                        <div class="bg-white rounded-lg shadow-lg p-6">
                            <div class="text-center mb-4">
                                <h3 class="text-xl font-bold text-gray-800">
                                    <?php echo $lang === 'ar' ? 'خريطة ماليزيا' : 'Malaysia Map'; ?>
                                </h3>
                            </div>
                            
                            <!-- SVG Map -->
                            <div class="flex justify-center">
                                <svg class="malaysia-map" width="600" height="400" viewBox="0 0 600 400" xmlns="http://www.w3.org/2000/svg">
                                    <!-- Peninsular Malaysia -->
                                    
                                    <!-- Perlis -->
                                    <path id="perlis" class="map-state" data-state="perlis" 
                                          d="M 180 80 L 200 75 L 210 85 L 205 95 L 185 90 Z" />
                                    
                                    <!-- Kedah -->
                                    <path id="kedah" class="map-state" data-state="kedah" 
                                          d="M 170 90 L 210 85 L 220 110 L 200 130 L 175 125 L 165 105 Z" />
                                    
                                    <!-- Penang -->
                                    <circle id="penang" class="map-state" data-state="penang" 
                                            cx="160" cy="120" r="8" />
                                    
                                    <!-- Perak -->
                                    <path id="perak" class="map-state" data-state="perak" 
                                          d="M 175 125 L 220 110 L 240 140 L 235 180 L 200 185 L 180 160 Z" />
                                    
                                    <!-- Selangor -->
                                    <path id="selangor" class="map-state" data-state="selangor" 
                                          d="M 200 185 L 235 180 L 245 200 L 230 220 L 210 215 Z" />
                                    
                                    <!-- Kuala Lumpur -->
                                    <circle id="kuala-lumpur" class="map-state" data-state="kuala-lumpur" 
                                            cx="220" cy="200" r="6" />
                                    
                                    <!-- Putrajaya -->
                                    <circle id="putrajaya" class="map-state" data-state="putrajaya" 
                                            cx="225" cy="210" r="4" />
                                    
                                    <!-- Negeri Sembilan -->
                                    <path id="negeri-sembilan" class="map-state" data-state="negeri-sembilan" 
                                          d="M 210 215 L 245 200 L 255 225 L 240 240 L 220 235 Z" />
                                    
                                    <!-- Melaka -->
                                    <path id="melaka" class="map-state" data-state="melaka" 
                                          d="M 220 235 L 240 240 L 245 255 L 230 260 L 215 250 Z" />
                                    
                                    <!-- Johor -->
                                    <path id="johor" class="map-state" data-state="johor" 
                                          d="M 215 250 L 255 225 L 280 240 L 290 270 L 270 290 L 240 285 L 220 270 Z" />
                                    
                                    <!-- Pahang -->
                                    <path id="pahang" class="map-state" data-state="pahang" 
                                          d="M 240 140 L 280 135 L 320 150 L 330 180 L 310 210 L 280 240 L 255 225 L 245 200 L 235 180 Z" />
                                    
                                    <!-- Terengganu -->
                                    <path id="terengganu" class="map-state" data-state="terengganu" 
                                          d="M 280 135 L 320 130 L 340 140 L 350 170 L 330 180 L 320 150 Z" />
                                    
                                    <!-- Kelantan -->
                                    <path id="kelantan" class="map-state" data-state="kelantan" 
                                          d="M 220 110 L 280 105 L 320 130 L 280 135 L 240 140 Z" />
                                    
                                    <!-- East Malaysia -->
                                    
                                    <!-- Sabah -->
                                    <path id="sabah" class="map-state" data-state="sabah" 
                                          d="M 420 120 L 480 115 L 520 130 L 530 160 L 510 180 L 470 175 L 430 165 L 410 145 Z" />
                                    
                                    <!-- Sarawak -->
                                    <path id="sarawak" class="map-state" data-state="sarawak" 
                                          d="M 380 180 L 470 175 L 510 180 L 520 210 L 500 240 L 450 245 L 400 240 L 370 220 L 360 200 Z" />
                                    
                                    <!-- Labuan -->
                                    <circle id="labuan" class="map-state" data-state="labuan" 
                                            cx="440" cy="150" r="4" />
                                    
                                    <!-- State Labels -->
                                    <text x="190" y="85" text-anchor="middle" class="text-xs font-semibold fill-gray-600">Perlis</text>
                                    <text x="190" y="110" text-anchor="middle" class="text-xs font-semibold fill-gray-600">Kedah</text>
                                    <text x="160" y="135" text-anchor="middle" class="text-xs font-semibold fill-gray-600">Penang</text>
                                    <text x="200" y="155" text-anchor="middle" class="text-xs font-semibold fill-gray-600">Perak</text>
                                    <text x="220" y="200" text-anchor="middle" class="text-xs font-semibold fill-gray-600">Selangor</text>
                                    <text x="220" y="190" text-anchor="middle" class="text-xs font-semibold fill-gray-600">KL</text>
                                    <text x="235" y="230" text-anchor="middle" class="text-xs font-semibold fill-gray-600">N.Sembilan</text>
                                    <text x="230" y="255" text-anchor="middle" class="text-xs font-semibold fill-gray-600">Melaka</text>
                                    <text x="250" y="270" text-anchor="middle" class="text-xs font-semibold fill-gray-600">Johor</text>
                                    <text x="280" y="180" text-anchor="middle" class="text-xs font-semibold fill-gray-600">Pahang</text>
                                    <text x="330" y="155" text-anchor="middle" class="text-xs font-semibold fill-gray-600">Terengganu</text>
                                    <text x="250" y="120" text-anchor="middle" class="text-xs font-semibold fill-gray-600">Kelantan</text>
                                    <text x="470" y="150" text-anchor="middle" class="text-xs font-semibold fill-gray-600">Sabah</text>
                                    <text x="440" y="215" text-anchor="middle" class="text-xs font-semibold fill-gray-600">Sarawak</text>
                                </svg>
                            </div>
                            
                            <!-- Map Legend -->
                            <div class="mt-6 text-center">
                                <div class="inline-flex items-center space-x-4 text-sm text-gray-600">
                                    <div class="flex items-center">
                                        <div class="w-4 h-4 bg-gray-300 rounded mr-2"></div>
                                        <span><?php echo $lang === 'ar' ? 'انقر للاستكشاف' : 'Click to explore'; ?></span>
                                    </div>
                                    <div class="flex items-center">
                                        <div class="w-4 h-4 bg-primary-500 rounded mr-2"></div>
                                        <span><?php echo $lang === 'ar' ? 'محدد' : 'Selected'; ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- State Information Panel -->
                    <div class="lg:col-span-1">
                        <div id="state-info" class="bg-white rounded-lg shadow-lg p-6 min-h-96">
                            <div id="default-info" class="text-center">
                                <div class="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <svg class="w-8 h-8 text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    </svg>
                                </div>
                                <h3 class="text-xl font-bold text-gray-800 mb-4">
                                    <?php echo $lang === 'ar' ? 'اختر ولاية من الخريطة' : 'Select a state from the map'; ?>
                                </h3>
                                <p class="text-gray-600">
                                    <?php echo $lang === 'ar' ? 
                                        'انقر على أي ولاية في الخريطة لمعرفة أهم المعالم السياحية والأنشطة المتاحة فيها.' : 
                                        'Click on any state in the map to learn about its top tourist attractions and available activities.'; ?>
                                </p>
                            </div>
                            
                            <div id="state-details" class="hidden">
                                <!-- State details will be populated by JavaScript -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Quick Stats Section -->
        <section class="py-16 bg-gray-100">
            <div class="max-w-7xl mx-auto px-4">
                <div class="text-center mb-12 scroll-animate">
                    <h2 class="text-3xl font-bold mb-4 text-gray-800">
                        <?php echo $lang === 'ar' ? 'ماليزيا في أرقام' : 'Malaysia in Numbers'; ?>
                    </h2>
                </div>

                <div class="grid md:grid-cols-4 gap-8 text-center">
                    <div class="scroll-animate">
                        <div class="text-4xl font-bold text-primary-500 mb-2 counter" data-target="13">0</div>
                        <div class="text-gray-600"><?php echo $lang === 'ar' ? 'ولاية' : 'States'; ?></div>
                    </div>
                    <div class="scroll-animate">
                        <div class="text-4xl font-bold text-primary-500 mb-2 counter" data-target="3">0</div>
                        <div class="text-gray-600"><?php echo $lang === 'ar' ? 'أقاليم اتحادية' : 'Federal Territories'; ?></div>
                    </div>
                    <div class="scroll-animate">
                        <div class="text-4xl font-bold text-primary-500 mb-2 counter" data-target="100">0</div>
                        <div class="text-gray-600"><?php echo $lang === 'ar' ? 'معلم سياحي' : 'Tourist Attractions'; ?></div>
                    </div>
                    <div class="scroll-animate">
                        <div class="text-4xl font-bold text-primary-500 mb-2 counter" data-target="32">0</div>
                        <div class="text-gray-600"><?php echo $lang === 'ar' ? 'مليون زائر سنوياً' : 'Million Visitors/Year'; ?></div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Popular Destinations Grid -->
        <section class="py-16 bg-white">
            <div class="max-w-7xl mx-auto px-4">
                <div class="text-center mb-12 scroll-animate">
                    <h2 class="text-3xl font-bold mb-4 text-gray-800">
                        <?php echo $lang === 'ar' ? 'أشهر الوجهات السياحية' : 'Most Popular Destinations'; ?>
                    </h2>
                    <p class="text-lg text-gray-600">
                        <?php echo $lang === 'ar' ? 'اكتشف أجمل الأماكن في ماليزيا' : 'Discover the most beautiful places in Malaysia'; ?>
                    </p>
                </div>

                <div class="grid md:grid-cols-3 lg:grid-cols-4 gap-6">
                    <div class="destination-card bg-white rounded-lg shadow-lg overflow-hidden cursor-pointer" onclick="selectState('kuala-lumpur')">
                        <img src="assets/images/states/kuala-lumpur.jpg" alt="Kuala Lumpur" class="w-full h-32 object-cover">
                        <div class="p-4">
                            <h3 class="font-bold text-gray-800"><?php echo $lang === 'ar' ? 'كوالالمبور' : 'Kuala Lumpur'; ?></h3>
                            <p class="text-sm text-gray-600"><?php echo $lang === 'ar' ? 'العاصمة الحديثة' : 'Modern Capital'; ?></p>
                        </div>
                    </div>

                    <div class="destination-card bg-white rounded-lg shadow-lg overflow-hidden cursor-pointer" onclick="selectState('penang')">
                        <img src="assets/images/states/penang.jpg" alt="Penang" class="w-full h-32 object-cover">
                        <div class="p-4">
                            <h3 class="font-bold text-gray-800"><?php echo $lang === 'ar' ? 'بينانغ' : 'Penang'; ?></h3>
                            <p class="text-sm text-gray-600"><?php echo $lang === 'ar' ? 'لؤلؤة الشرق' : 'Pearl of the Orient'; ?></p>
                        </div>
                    </div>

                    <div class="destination-card bg-white rounded-lg shadow-lg overflow-hidden cursor-pointer" onclick="selectState('sabah')">
                        <img src="assets/images/states/sabah.jpg" alt="Sabah" class="w-full h-32 object-cover">
                        <div class="p-4">
                            <h3 class="font-bold text-gray-800"><?php echo $lang === 'ar' ? 'صباح' : 'Sabah'; ?></h3>
                            <p class="text-sm text-gray-600"><?php echo $lang === 'ar' ? 'أرض تحت الرياح' : 'Land Below the Wind'; ?></p>
                        </div>
                    </div>

                    <div class="destination-card bg-white rounded-lg shadow-lg overflow-hidden cursor-pointer" onclick="selectState('sarawak')">
                        <img src="assets/images/states/sarawak.jpg" alt="Sarawak" class="w-full h-32 object-cover">
                        <div class="p-4">
                            <h3 class="font-bold text-gray-800"><?php echo $lang === 'ar' ? 'ساراواك' : 'Sarawak'; ?></h3>
                            <p class="text-sm text-gray-600"><?php echo $lang === 'ar' ? 'أرض القرود' : 'Land of the Hornbills'; ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- CTA Section -->
        <section class="py-20 bg-primary-600 text-white">
            <div class="max-w-4xl mx-auto px-4 text-center">
                <h2 class="text-4xl font-bold mb-6">
                    <?php echo $lang === 'ar' ? 'جاهز لاستكشاف ماليزيا؟' : 'Ready to Explore Malaysia?'; ?>
                </h2>
                <p class="text-xl mb-8 opacity-90">
                    <?php echo $lang === 'ar' ?
                        'تواصل معنا لتخطيط رحلتك المثالية إلى أي ولاية في ماليزيا' :
                        'Contact us to plan your perfect trip to any state in Malaysia'; ?>
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="contact.php?lang=<?php echo $lang; ?>"
                       class="bg-white text-primary-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                        <?php echo $lang === 'ar' ? 'خطط رحلتك' : 'Plan Your Trip'; ?>
                    </a>
                    <a href="https://wa.me/<?php echo WHATSAPP_NUMBER; ?>" target="_blank"
                       class="bg-green-500 hover:bg-green-600 text-white px-8 py-3 rounded-lg font-semibold transition-colors flex items-center justify-center">
                        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 ************* 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
                        </svg>
                        <?php echo $lang === 'ar' ? 'واتساب' : 'WhatsApp'; ?>
                    </a>
                </div>
            </div>
        </section>
    </main>

    <?php
    if (file_exists('includes/footer.php')) {
        include 'includes/footer.php';
    } else {
        // Simple footer fallback
        echo '<footer class="bg-gray-800 text-white py-8 text-center"><p>&copy; 2024 Malaysia Tourism Trips</p></footer>';
    }
    ?>

    <!-- Custom JavaScript -->
    <script>
        // Fallback if main.js doesn't exist
        if (typeof forceContentVisible === 'undefined') {
            function forceContentVisible() {
                const elements = document.querySelectorAll('.scroll-animate, .article-card, .destination-card, .gallery-item');
                elements.forEach(element => {
                    element.style.opacity = '1';
                    element.style.transform = 'translateY(0)';
                    element.style.visibility = 'visible';
                });
            }

            document.addEventListener('DOMContentLoaded', forceContentVisible);
        }
    </script>
    <script src="assets/js/main.js" onerror="console.log('main.js not found, using fallback')"></script>

    <script>
        // Tourist data for each state
        const stateData = {
            'kuala-lumpur': {
                name: {
                    en: 'Kuala Lumpur',
                    ar: 'كوالالمبور'
                },
                description: {
                    en: 'The vibrant capital city of Malaysia, known for its modern skyline and cultural diversity.',
                    ar: 'العاصمة النابضة بالحياة في ماليزيا، المعروفة بأفقها الحديث وتنوعها الثقافي.'
                },
                attractions: [
                    {
                        name: { en: 'Petronas Twin Towers', ar: 'برجا بتروناس التوأم' },
                        description: { en: 'Iconic twin skyscrapers and symbol of Malaysia', ar: 'ناطحات السحاب التوأم الشهيرة ورمز ماليزيا' },
                        type: { en: 'Architecture', ar: 'عمارة' }
                    },
                    {
                        name: { en: 'KL Tower', ar: 'برج كوالالمبور' },
                        description: { en: 'Telecommunications tower with observation deck', ar: 'برج الاتصالات مع منصة المراقبة' },
                        type: { en: 'Landmark', ar: 'معلم' }
                    },
                    {
                        name: { en: 'Batu Caves', ar: 'كهوف باتو' },
                        description: { en: 'Hindu temple complex in limestone caves', ar: 'مجمع معابد هندوسية في كهوف الحجر الجيري' },
                        type: { en: 'Religious Site', ar: 'موقع ديني' }
                    },
                    {
                        name: { en: 'Central Market', ar: 'السوق المركزي' },
                        description: { en: 'Cultural market with local arts and crafts', ar: 'سوق ثقافي بالفنون والحرف المحلية' },
                        type: { en: 'Shopping', ar: 'تسوق' }
                    }
                ],
                image: 'assets/images/states/kuala-lumpur.jpg'
            },
            'penang': {
                name: {
                    en: 'Penang',
                    ar: 'بينانغ'
                },
                description: {
                    en: 'Known as the Pearl of the Orient, famous for its UNESCO World Heritage George Town.',
                    ar: 'تُعرف بلؤلؤة الشرق، مشهورة بجورج تاون المدرجة في قائمة التراث العالمي لليونسكو.'
                },
                attractions: [
                    {
                        name: { en: 'George Town UNESCO Site', ar: 'موقع جورج تاون اليونسكو' },
                        description: { en: 'Historic city center with colonial architecture', ar: 'مركز المدينة التاريخي بالعمارة الاستعمارية' },
                        type: { en: 'Heritage', ar: 'تراث' }
                    },
                    {
                        name: { en: 'Penang Hill', ar: 'تل بينانغ' },
                        description: { en: 'Hill station with panoramic views', ar: 'محطة تلال مع إطلالات بانورامية' },
                        type: { en: 'Nature', ar: 'طبيعة' }
                    },
                    {
                        name: { en: 'Street Art', ar: 'فن الشارع' },
                        description: { en: 'Famous murals and street art throughout the city', ar: 'جداريات وفن شارع مشهور في جميع أنحاء المدينة' },
                        type: { en: 'Art', ar: 'فن' }
                    },
                    {
                        name: { en: 'Clan Houses', ar: 'بيوت العشائر' },
                        description: { en: 'Traditional Chinese clan houses and temples', ar: 'بيوت العشائر الصينية التقليدية والمعابد' },
                        type: { en: 'Culture', ar: 'ثقافة' }
                    }
                ],
                image: 'assets/images/states/penang.jpg'
            },
            'sabah': {
                name: {
                    en: 'Sabah',
                    ar: 'صباح'
                },
                description: {
                    en: 'Land Below the Wind, famous for Mount Kinabalu and diverse wildlife.',
                    ar: 'أرض تحت الرياح، مشهورة بجبل كينابالو والحياة البرية المتنوعة.'
                },
                attractions: [
                    {
                        name: { en: 'Mount Kinabalu', ar: 'جبل كينابالو' },
                        description: { en: 'Highest mountain in Southeast Asia', ar: 'أعلى جبل في جنوب شرق آسيا' },
                        type: { en: 'Mountain', ar: 'جبل' }
                    },
                    {
                        name: { en: 'Sipadan Island', ar: 'جزيرة سيبادان' },
                        description: { en: 'World-class diving destination', ar: 'وجهة غوص عالمية المستوى' },
                        type: { en: 'Diving', ar: 'غوص' }
                    },
                    {
                        name: { en: 'Sepilok Orangutan Centre', ar: 'مركز إنسان الغاب سيبيلوك' },
                        description: { en: 'Orangutan rehabilitation center', ar: 'مركز إعادة تأهيل إنسان الغاب' },
                        type: { en: 'Wildlife', ar: 'حياة برية' }
                    },
                    {
                        name: { en: 'Kinabatangan River', ar: 'نهر كينابتانغان' },
                        description: { en: 'Wildlife river cruise destination', ar: 'وجهة رحلات نهرية للحياة البرية' },
                        type: { en: 'River Cruise', ar: 'رحلة نهرية' }
                    }
                ],
                image: 'assets/images/states/sabah.jpg'
            },
            'sarawak': {
                name: { en: 'Sarawak', ar: 'ساراواك' },
                description: { en: 'Land of the Hornbills, known for its caves, national parks, and indigenous culture.', ar: 'أرض القرود، معروفة بكهوفها ومتنزهاتها الوطنية وثقافتها الأصلية.' },
                attractions: [
                    { name: { en: 'Mulu Caves', ar: 'كهوف مولو' }, description: { en: 'UNESCO World Heritage cave system', ar: 'نظام كهوف التراث العالمي لليونسكو' }, type: { en: 'Caves', ar: 'كهوف' } },
                    { name: { en: 'Kuching Waterfront', ar: 'واجهة كوتشينغ البحرية' }, description: { en: 'Scenic riverfront promenade', ar: 'كورنيش نهري خلاب' }, type: { en: 'Waterfront', ar: 'واجهة بحرية' } },
                    { name: { en: 'Bako National Park', ar: 'متنزه باكو الوطني' }, description: { en: 'Oldest national park in Sarawak', ar: 'أقدم متنزه وطني في ساراواك' }, type: { en: 'National Park', ar: 'متنزه وطني' } },
                    { name: { en: 'Longhouse Visits', ar: 'زيارات البيوت الطويلة' }, description: { en: 'Traditional indigenous community homes', ar: 'منازل المجتمعات الأصلية التقليدية' }, type: { en: 'Culture', ar: 'ثقافة' } }
                ],
                image: 'assets/images/states/sarawak.jpg'
            }
        };

        // Add more states data
        stateData['johor'] = {
            name: { en: 'Johor', ar: 'جوهور' },
            description: { en: 'Southern gateway to Malaysia, known for theme parks and historical sites.', ar: 'البوابة الجنوبية لماليزيا، معروفة بمدن الألعاب والمواقع التاريخية.' },
            attractions: [
                { name: { en: 'Legoland Malaysia', ar: 'ليجولاند ماليزيا' }, description: { en: 'Family theme park with rides and attractions', ar: 'مدينة ألعاب عائلية مع الألعاب والمعالم' }, type: { en: 'Theme Park', ar: 'مدينة ألعاب' } },
                { name: { en: 'Sultan Abu Bakar Mosque', ar: 'مسجد السلطان أبو بكر' }, description: { en: 'Beautiful Victorian-style mosque', ar: 'مسجد جميل بالطراز الفيكتوري' }, type: { en: 'Religious Site', ar: 'موقع ديني' } },
                { name: { en: 'Desaru Beach', ar: 'شاطئ ديسارو' }, description: { en: 'Popular beach resort destination', ar: 'وجهة منتجع شاطئي شهيرة' }, type: { en: 'Beach', ar: 'شاطئ' } },
                { name: { en: 'Johor Bahru City Square', ar: 'ميدان مدينة جوهور بهرو' }, description: { en: 'Major shopping and entertainment complex', ar: 'مجمع تسوق وترفيه رئيسي' }, type: { en: 'Shopping', ar: 'تسوق' } }
            ],
            image: 'assets/images/states/johor.jpg'
        };

        // Current language
        const currentLang = '<?php echo $lang; ?>';

        // Initialize map functionality
        document.addEventListener('DOMContentLoaded', function() {
            initializeMap();
        });

        function initializeMap() {
            const mapStates = document.querySelectorAll('.map-state');

            mapStates.forEach(state => {
                state.addEventListener('click', function() {
                    const stateId = this.getAttribute('data-state');
                    selectState(stateId);
                });

                state.addEventListener('mouseenter', function() {
                    if (!this.classList.contains('active')) {
                        this.style.fill = '#0ea5e9';
                    }
                });

                state.addEventListener('mouseleave', function() {
                    if (!this.classList.contains('active')) {
                        this.style.fill = '#e5e7eb';
                    }
                });
            });
        }

        function selectState(stateId) {
            // Remove active class from all states
            document.querySelectorAll('.map-state').forEach(state => {
                state.classList.remove('active');
                state.style.fill = '#e5e7eb';
            });

            // Add active class to selected state
            const selectedState = document.getElementById(stateId);
            if (selectedState) {
                selectedState.classList.add('active');
                selectedState.style.fill = '#0284c7';
            }

            // Show state information
            showStateInfo(stateId);
        }

        function showStateInfo(stateId) {
            const defaultInfo = document.getElementById('default-info');
            const stateDetails = document.getElementById('state-details');

            if (stateData[stateId]) {
                const data = stateData[stateId];

                defaultInfo.classList.add('hidden');
                stateDetails.classList.remove('hidden');

                stateDetails.innerHTML = `
                    <div class="text-center mb-6">
                        <img src="${data.image}" alt="${data.name[currentLang]}" class="w-full h-32 object-cover rounded-lg mb-4" onerror="this.src='assets/images/placeholder.jpg'">
                        <h3 class="text-2xl font-bold text-gray-800 mb-2">${data.name[currentLang]}</h3>
                        <p class="text-gray-600">${data.description[currentLang]}</p>
                    </div>

                    <div class="space-y-4">
                        <h4 class="text-lg font-semibold text-gray-800 mb-3">
                            ${currentLang === 'ar' ? 'أهم المعالم السياحية:' : 'Top Tourist Attractions:'}
                        </h4>
                        ${data.attractions.map(attraction => `
                            <div class="tourist-attraction bg-gray-50 p-4 rounded-lg">
                                <div class="flex justify-between items-start mb-2">
                                    <h5 class="font-semibold text-gray-800">${attraction.name[currentLang]}</h5>
                                    <span class="text-xs bg-primary-100 text-primary-600 px-2 py-1 rounded-full">${attraction.type[currentLang]}</span>
                                </div>
                                <p class="text-sm text-gray-600">${attraction.description[currentLang]}</p>
                            </div>
                        `).join('')}
                    </div>

                    <div class="mt-6 pt-6 border-t border-gray-200">
                        <a href="contact.php?state=${stateId}&lang=${currentLang}"
                           class="w-full bg-primary-500 hover:bg-primary-600 text-white px-4 py-3 rounded-lg font-semibold transition-colors text-center block">
                            ${currentLang === 'ar' ? 'خطط رحلتك إلى ' + data.name[currentLang] : 'Plan Your Trip to ' + data.name[currentLang]}
                        </a>
                    </div>
                `;
            } else {
                // Show default message for states without data
                defaultInfo.classList.add('hidden');
                stateDetails.classList.remove('hidden');

                stateDetails.innerHTML = `
                    <div class="text-center">
                        <div class="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold text-gray-800 mb-4">
                            ${currentLang === 'ar' ? 'معلومات قريباً' : 'Information Coming Soon'}
                        </h3>
                        <p class="text-gray-600 mb-6">
                            ${currentLang === 'ar' ?
                                'نعمل على إضافة معلومات مفصلة عن هذه الولاية. تواصل معنا للحصول على معلومات أكثر.' :
                                'We are working on adding detailed information about this state. Contact us for more information.'}
                        </p>
                        <a href="contact.php?state=${stateId}&lang=${currentLang}"
                           class="bg-primary-500 hover:bg-primary-600 text-white px-6 py-3 rounded-lg font-semibold transition-colors">
                            ${currentLang === 'ar' ? 'تواصل معنا' : 'Contact Us'}
                        </a>
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
