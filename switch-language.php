<?php
session_start();

// Get the requested language
$lang = $_GET['lang'] ?? 'en';

// Validate language
if (!in_array($lang, ['en', 'ar'])) {
    $lang = 'en';
}

// Set language in session
$_SESSION['lang'] = $lang;

// Get the referrer URL to redirect back
$referrer = $_SERVER['HTTP_REFERER'] ?? 'index.php';

// Remove any existing lang parameter from the URL
$parsedUrl = parse_url($referrer);
if (isset($parsedUrl['query'])) {
    parse_str($parsedUrl['query'], $queryParams);
    unset($queryParams['lang']);
    $queryString = http_build_query($queryParams);
    $referrer = $parsedUrl['scheme'] . '://' . $parsedUrl['host'] . $parsedUrl['path'];
    if (!empty($queryString)) {
        $referrer .= '?' . $queryString;
    }
}

// Add the new language parameter
$separator = strpos($referrer, '?') !== false ? '&' : '?';
$referrer .= $separator . 'lang=' . $lang;

// Redirect back to the previous page
header('Location: ' . $referrer);
exit;
?>
