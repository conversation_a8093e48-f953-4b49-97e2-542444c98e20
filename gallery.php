<?php
session_start();
require_once 'includes/config.php';
require_once 'includes/Database.php';
require_once 'includes/ContentManager.php';

// Handle language switching
if (isset($_GET['lang']) && in_array($_GET['lang'], SUPPORTED_LANGS)) {
    $_SESSION['lang'] = $_GET['lang'];
}

$db = new Database();
$contentManager = new ContentManager($db);

// Get current language
$lang = $_SESSION['lang'] ?? DEFAULT_LANG;
$isRTL = $lang === 'ar';

// Get gallery images and categories
$galleryImages = $contentManager->getGalleryImages();
$categories = $contentManager->getGalleryCategories($lang);
?>

<!DOCTYPE html>
<html lang="<?php echo $lang; ?>" dir="<?php echo $isRTL ? 'rtl' : 'ltr'; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $lang === 'ar' ? 'معرض الصور - رحلات ماليزيا السياحية' : 'Photo Gallery - Malaysia Tourism Trips'; ?></title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <?php if ($lang === 'ar'): ?>
        <link href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <?php else: ?>
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <?php endif; ?>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'arabic': ['IBM Plex Sans Arabic', 'sans-serif'],
                        'english': ['Inter', 'sans-serif']
                    },
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1'
                        },
                        secondary: {
                            500: '#f59e0b',
                            600: '#d97706'
                        }
                    }
                }
            }
        }
    </script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    
    <!-- GSAP for animations -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js"></script>
</head>
<body class="<?php echo $lang === 'ar' ? 'font-arabic' : 'font-english'; ?> bg-gray-50">
    
    <?php include 'includes/header.php'; ?>
    
    <main class="pt-16">
        <!-- Hero Section -->
        <section class="relative h-96 bg-gradient-to-r from-primary-600 to-primary-800 overflow-hidden">
            <div class="absolute inset-0 bg-black bg-opacity-30"></div>
            <div class="relative z-10 flex items-center justify-center h-full text-white text-center">
                <div class="max-w-4xl mx-auto px-4">
                    <h1 class="text-5xl md:text-6xl font-bold mb-4 hero-title">
                        <?php echo $lang === 'ar' ? 'معرض الصور' : 'Photo Gallery'; ?>
                    </h1>
                    <p class="text-xl md:text-2xl hero-subtitle">
                        <?php echo $lang === 'ar' ? 'استكشف جمال ماليزيا من خلال صورنا المذهلة' : 'Explore the beauty of Malaysia through our stunning photos'; ?>
                    </p>
                </div>
            </div>
        </section>

        <!-- Filter Section -->
        <section class="py-12 bg-white">
            <div class="max-w-7xl mx-auto px-4">
                <div class="text-center mb-8">
                    <h2 class="text-3xl font-bold mb-4 text-gray-800">
                        <?php echo $lang === 'ar' ? 'تصفح حسب الفئة' : 'Browse by Category'; ?>
                    </h2>
                    <p class="text-lg text-gray-600">
                        <?php echo $lang === 'ar' ? 'اختر فئة لعرض الصور المتعلقة بها' : 'Select a category to view related photos'; ?>
                    </p>
                </div>
                
                <div class="flex flex-wrap justify-center gap-4 mb-8">
                    <button class="filter-button active" data-filter="all">
                        <?php echo $lang === 'ar' ? 'جميع الصور' : 'All Photos'; ?>
                    </button>
                    <?php foreach ($categories as $category): ?>
                        <button class="filter-button" data-filter="<?php echo strtolower(str_replace(' ', '-', $category['category'])); ?>">
                            <?php echo $category['category']; ?>
                        </button>
                    <?php endforeach; ?>
                </div>
            </div>
        </section>

        <!-- Gallery Grid -->
        <section class="py-12 bg-gray-50">
            <div class="max-w-7xl mx-auto px-4">
                <div id="gallery-grid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                    <?php foreach ($galleryImages as $image): ?>
                        <div class="gallery-item cursor-pointer group" 
                             data-category="<?php echo strtolower(str_replace(' ', '-', $image['category'] ?? 'general')); ?>"
                             data-image="<?php echo $image['url']; ?>"
                             data-title="<?php echo htmlspecialchars($image['title'] ?? ''); ?>"
                             data-description="<?php echo htmlspecialchars($image['description'] ?? ''); ?>">
                            <div class="relative overflow-hidden rounded-lg shadow-lg bg-white">
                                <div class="aspect-w-4 aspect-h-3">
                                    <img src="<?php echo $image['thumbnail'] ?? $image['url']; ?>" 
                                         alt="<?php echo htmlspecialchars($image['title'] ?? ''); ?>"
                                         class="w-full h-64 object-cover transition-transform duration-300 group-hover:scale-110">
                                </div>
                                <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all duration-300 flex items-center justify-center">
                                    <div class="opacity-0 group-hover:opacity-100 transition-opacity duration-300 text-white text-center">
                                        <svg class="w-12 h-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7"></path>
                                        </svg>
                                        <p class="text-sm font-semibold">
                                            <?php echo $lang === 'ar' ? 'عرض الصورة' : 'View Image'; ?>
                                        </p>
                                    </div>
                                </div>
                                <?php if (!empty($image['title'])): ?>
                                    <div class="p-4">
                                        <h3 class="font-semibold text-gray-800 mb-1"><?php echo $image['title']; ?></h3>
                                        <?php if (!empty($image['description'])): ?>
                                            <p class="text-sm text-gray-600 line-clamp-2"><?php echo $image['description']; ?></p>
                                        <?php endif; ?>
                                        <?php if (!empty($image['category'])): ?>
                                            <span class="inline-block mt-2 px-2 py-1 bg-primary-100 text-primary-600 text-xs rounded-full">
                                                <?php echo $image['category']; ?>
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
                
                <!-- Load More Button -->
                <div class="text-center mt-12">
                    <button id="load-more" class="bg-primary-500 hover:bg-primary-600 text-white px-8 py-3 rounded-lg font-semibold transition-colors">
                        <?php echo $lang === 'ar' ? 'تحميل المزيد' : 'Load More'; ?>
                    </button>
                </div>
            </div>
        </section>

        <!-- Stats Section -->
        <section class="py-20 bg-white">
            <div class="max-w-7xl mx-auto px-4">
                <div class="grid md:grid-cols-4 gap-8 text-center">
                    <div class="scroll-animate">
                        <div class="text-4xl font-bold text-primary-500 mb-2 counter" data-target="500">0</div>
                        <div class="text-gray-600"><?php echo $lang === 'ar' ? 'صورة' : 'Photos'; ?></div>
                    </div>
                    <div class="scroll-animate">
                        <div class="text-4xl font-bold text-primary-500 mb-2 counter" data-target="50">0</div>
                        <div class="text-gray-600"><?php echo $lang === 'ar' ? 'وجهة' : 'Destinations'; ?></div>
                    </div>
                    <div class="scroll-animate">
                        <div class="text-4xl font-bold text-primary-500 mb-2 counter" data-target="15">0</div>
                        <div class="text-gray-600"><?php echo $lang === 'ar' ? 'فئة' : 'Categories'; ?></div>
                    </div>
                    <div class="scroll-animate">
                        <div class="text-4xl font-bold text-primary-500 mb-2 counter" data-target="1000">0</div>
                        <div class="text-gray-600"><?php echo $lang === 'ar' ? 'مشاهدة' : 'Views'; ?></div>
                    </div>
                </div>
            </div>
        </section>

        <!-- CTA Section -->
        <section class="py-20 bg-primary-600 text-white">
            <div class="max-w-4xl mx-auto px-4 text-center">
                <h2 class="text-4xl font-bold mb-6">
                    <?php echo $lang === 'ar' ? 'شارك ذكرياتك معنا' : 'Share Your Memories With Us'; ?>
                </h2>
                <p class="text-xl mb-8 opacity-90">
                    <?php echo $lang === 'ar' ? 
                        'هل لديك صور جميلة من رحلتك إلى ماليزيا؟ شاركها معنا!' : 
                        'Do you have beautiful photos from your trip to Malaysia? Share them with us!'; ?>
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="contact.php" class="bg-white text-primary-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                        <?php echo $lang === 'ar' ? 'شارك صورك' : 'Share Your Photos'; ?>
                    </a>
                    <a href="https://wa.me/<?php echo WHATSAPP_NUMBER; ?>" target="_blank" 
                       class="bg-green-500 hover:bg-green-600 text-white px-8 py-3 rounded-lg font-semibold transition-colors flex items-center justify-center">
                        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
                        </svg>
                        <?php echo $lang === 'ar' ? 'واتساب' : 'WhatsApp'; ?>
                    </a>
                </div>
            </div>
        </section>
    </main>

    <!-- Lightbox Modal -->
    <div id="lightbox" class="fixed inset-0 bg-black bg-opacity-90 z-50 hidden items-center justify-center p-4">
        <div class="relative max-w-4xl max-h-full">
            <img id="lightbox-image" src="" alt="" class="max-w-full max-h-full object-contain">
            <button id="lightbox-close" class="absolute top-4 <?php echo $isRTL ? 'left-4' : 'right-4'; ?> text-white hover:text-gray-300 transition-colors">
                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
            <div id="lightbox-info" class="absolute bottom-4 left-4 right-4 text-white text-center">
                <h3 id="lightbox-title" class="text-xl font-bold mb-2"></h3>
                <p id="lightbox-description" class="text-gray-300"></p>
            </div>
        </div>
    </div>
    
    <?php include 'includes/footer.php'; ?>
    
    <!-- Custom JavaScript -->
    <script src="assets/js/main.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize gallery filter
            if (typeof MalaysiaTourism !== 'undefined' && MalaysiaTourism.initGalleryFilter) {
                MalaysiaTourism.initGalleryFilter();
            } else {
                initGalleryFilter();
            }
            
            // Initialize lightbox
            initGalleryLightbox();
        });
        
        function initGalleryFilter() {
            const filterButtons = document.querySelectorAll('.filter-button');
            const galleryItems = document.querySelectorAll('.gallery-item');
            
            filterButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const filter = button.dataset.filter;
                    
                    // Update active button
                    filterButtons.forEach(btn => btn.classList.remove('active'));
                    button.classList.add('active');
                    
                    // Filter items
                    galleryItems.forEach(item => {
                        if (filter === 'all' || item.dataset.category === filter) {
                            item.style.display = 'block';
                            // Animate in
                            gsap.from(item, {
                                duration: 0.5,
                                scale: 0.8,
                                opacity: 0,
                                ease: 'power2.out'
                            });
                        } else {
                            item.style.display = 'none';
                        }
                    });
                });
            });
        }
        
        function initGalleryLightbox() {
            const galleryItems = document.querySelectorAll('.gallery-item');
            const lightbox = document.getElementById('lightbox');
            const lightboxImage = document.getElementById('lightbox-image');
            const lightboxTitle = document.getElementById('lightbox-title');
            const lightboxDescription = document.getElementById('lightbox-description');
            const lightboxClose = document.getElementById('lightbox-close');
            
            galleryItems.forEach(item => {
                item.addEventListener('click', () => {
                    const imageUrl = item.dataset.image;
                    const title = item.dataset.title;
                    const description = item.dataset.description;
                    
                    lightboxImage.src = imageUrl;
                    lightboxTitle.textContent = title;
                    lightboxDescription.textContent = description;
                    
                    lightbox.classList.remove('hidden');
                    lightbox.classList.add('flex');
                    document.body.style.overflow = 'hidden';
                });
            });
            
            function closeLightbox() {
                lightbox.classList.add('hidden');
                lightbox.classList.remove('flex');
                document.body.style.overflow = '';
                lightboxImage.src = '';
            }
            
            lightboxClose.addEventListener('click', closeLightbox);
            
            lightbox.addEventListener('click', (e) => {
                if (e.target === lightbox) {
                    closeLightbox();
                }
            });
            
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape' && !lightbox.classList.contains('hidden')) {
                    closeLightbox();
                }
            });
        }
    </script>
</body>
</html>
