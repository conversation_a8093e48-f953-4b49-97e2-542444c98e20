<?php
session_start();
require_once 'includes/config.php';
require_once 'includes/Database.php';
require_once 'includes/ContentManager.php';

// Handle language switching
if (isset($_GET['lang']) && in_array($_GET['lang'], SUPPORTED_LANGS)) {
    $_SESSION['lang'] = $_GET['lang'];
}

$db = new Database();
$contentManager = new ContentManager($db);

// Get current language from session or default to English
$lang = $_SESSION['lang'] ?? DEFAULT_LANG;
$isRTL = $lang === 'ar';

// Get homepage content
$featuredDestinations = $contentManager->getFeaturedDestinations($lang, 6);
$recentArticles = $contentManager->getRecentArticles($lang, 3);
$testimonials = $contentManager->getTestimonials($lang);
$galleryImages = $contentManager->getGalleryImages(8);
?>

<!DOCTYPE html>
<html lang="<?php echo $lang; ?>" dir="<?php echo $isRTL ? 'rtl' : 'ltr'; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $lang === 'ar' ? 'رحلات ماليزيا السياحية' : 'Malaysia Tourism Trips'; ?></title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <?php if ($lang === 'ar'): ?>
        <link href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <?php else: ?>
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <?php endif; ?>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'arabic': ['IBM Plex Sans Arabic', 'sans-serif'],
                        'english': ['Inter', 'sans-serif']
                    },
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1'
                        },
                        secondary: {
                            500: '#f59e0b',
                            600: '#d97706'
                        }
                    }
                }
            }
        }
    </script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">

    <!-- IMMEDIATE FIX: Force content visibility -->
    <style>
        .scroll-animate,
        .article-card,
        .destination-card,
        .gallery-item {
            opacity: 1 !important;
            transform: translateY(0) !important;
            visibility: visible !important;
        }
    </style>
    
    <!-- GSAP for animations -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js"></script>
</head>
<body class="<?php echo $lang === 'ar' ? 'font-arabic' : 'font-english'; ?> bg-gray-50">
    
    <?php include 'includes/header.php'; ?>
    
    <main>
        <!-- Hero Section -->
        <section id="hero" class="relative h-screen overflow-hidden">
            <div class="absolute inset-0 bg-black bg-opacity-40 z-10"></div>
            <video autoplay muted loop class="absolute inset-0 w-full h-full object-cover">
                <source src="assets/videos/hero-video.mp4" type="video/mp4">
            </video>
            <div class="relative z-20 flex items-center justify-center h-full text-white text-center">
                <div class="max-w-4xl mx-auto px-4">
                    <h1 class="text-5xl md:text-7xl font-bold mb-6 hero-title">
                        <?php echo $lang === 'ar' ? 'اكتشف جمال ماليزيا' : 'Discover Malaysia\'s Beauty'; ?>
                    </h1>
                    <p class="text-xl md:text-2xl mb-8 hero-subtitle">
                        <?php echo $lang === 'ar' ? 'رحلات سياحية استثنائية إلى أجمل الوجهات' : 'Exceptional tourism trips to the most beautiful destinations'; ?>
                    </p>
                    <button class="bg-primary-500 hover:bg-primary-600 text-white px-8 py-4 rounded-lg text-lg font-semibold transition-all duration-300 transform hover:scale-105">
                        <?php echo $lang === 'ar' ? 'استكشف الرحلات' : 'Explore Trips'; ?>
                    </button>
                </div>
            </div>
        </section>

        <!-- Company Intro Section -->
        <section id="intro" class="py-20 bg-white">
            <div class="max-w-7xl mx-auto px-4">
                <div class="grid md:grid-cols-2 gap-12 items-center">
                    <div class="intro-text">
                        <h2 class="text-4xl font-bold mb-6 text-gray-800">
                            <?php echo $lang === 'ar' ? 'من نحن' : 'About Our Company'; ?>
                        </h2>
                        <p class="text-lg text-gray-600 mb-6 leading-relaxed">
                            <?php echo $lang === 'ar' ? 
                                'نحن شركة رائدة في مجال السياحة والسفر، متخصصون في تنظيم رحلات استثنائية إلى ماليزيا وأجمل الوجهات السياحية في العالم.' : 
                                'We are a leading company in tourism and travel, specializing in organizing exceptional trips to Malaysia and the world\'s most beautiful tourist destinations.'; ?>
                        </p>
                        <p class="text-lg text-gray-600 mb-8 leading-relaxed">
                            <?php echo $lang === 'ar' ? 
                                'مع سنوات من الخبرة والشغف، نقدم تجارب سفر لا تُنسى مع أفضل الخدمات والأسعار التنافسية.' : 
                                'With years of experience and passion, we provide unforgettable travel experiences with the best services and competitive prices.'; ?>
                        </p>
                        <div class="flex space-x-8">
                            <div class="text-center">
                                <div class="text-3xl font-bold text-primary-500">500+</div>
                                <div class="text-gray-600"><?php echo $lang === 'ar' ? 'عميل سعيد' : 'Happy Clients'; ?></div>
                            </div>
                            <div class="text-center">
                                <div class="text-3xl font-bold text-primary-500">50+</div>
                                <div class="text-gray-600"><?php echo $lang === 'ar' ? 'وجهة سياحية' : 'Destinations'; ?></div>
                            </div>
                            <div class="text-center">
                                <div class="text-3xl font-bold text-primary-500">5+</div>
                                <div class="text-gray-600"><?php echo $lang === 'ar' ? 'سنوات خبرة' : 'Years Experience'; ?></div>
                            </div>
                        </div>
                    </div>
                    <div class="intro-image">
                        <img src="assets/images/company-intro.jpg" alt="Company Introduction" class="rounded-lg shadow-lg w-full">
                    </div>
                </div>
            </div>
        </section>

        <!-- Featured Destinations Section -->
        <section id="destinations" class="py-20 bg-gray-100">
            <div class="max-w-7xl mx-auto px-4">
                <div class="text-center mb-16">
                    <h2 class="text-4xl font-bold mb-4 text-gray-800">
                        <?php echo $lang === 'ar' ? 'الوجهات المميزة' : 'Featured Destinations'; ?>
                    </h2>
                    <p class="text-lg text-gray-600 max-w-2xl mx-auto">
                        <?php echo $lang === 'ar' ? 
                            'اكتشف أجمل الوجهات السياحية التي نقدمها مع باقات مميزة وخدمات استثنائية' : 
                            'Discover the most beautiful tourist destinations we offer with special packages and exceptional services'; ?>
                    </p>
                </div>
                
                <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <?php foreach ($featuredDestinations as $destination): ?>
                    <div class="destination-card bg-white rounded-lg shadow-lg overflow-hidden transform hover:scale-105 transition-all duration-300">
                        <div class="relative overflow-hidden">
                            <img src="<?php echo $destination['image']; ?>" alt="<?php echo $destination['name']; ?>" class="w-full h-64 object-cover hover:scale-110 transition-transform duration-300">
                            <div class="absolute top-4 right-4 bg-secondary-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
                                <?php echo $destination['price']; ?>
                            </div>
                        </div>
                        <div class="p-6">
                            <h3 class="text-xl font-bold mb-2 text-gray-800"><?php echo $destination['name']; ?></h3>
                            <p class="text-gray-600 mb-4"><?php echo $destination['description']; ?></p>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-500"><?php echo $destination['duration']; ?></span>
                                <button class="bg-primary-500 hover:bg-primary-600 text-white px-4 py-2 rounded-lg text-sm font-semibold transition-colors">
                                    <?php echo $lang === 'ar' ? 'المزيد' : 'Learn More'; ?>
                                </button>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </section>

        <!-- Gallery Preview Section -->
        <section id="gallery-preview" class="py-20 bg-white">
            <div class="max-w-7xl mx-auto px-4">
                <div class="text-center mb-16">
                    <h2 class="text-4xl font-bold mb-4 text-gray-800">
                        <?php echo $lang === 'ar' ? 'معرض الصور' : 'Photo Gallery'; ?>
                    </h2>
                    <p class="text-lg text-gray-600 max-w-2xl mx-auto">
                        <?php echo $lang === 'ar' ? 
                            'شاهد أجمل اللحظات من رحلاتنا السياحية والذكريات الرائعة لعملائنا' : 
                            'View the most beautiful moments from our tourist trips and wonderful memories of our clients'; ?>
                    </p>
                </div>
                
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <?php foreach ($galleryImages as $index => $image): ?>
                    <div class="gallery-item relative overflow-hidden rounded-lg shadow-lg cursor-pointer transform hover:scale-105 transition-all duration-300" data-image="<?php echo $image['url']; ?>">
                        <img src="<?php echo $image['thumbnail']; ?>" alt="Gallery Image" class="w-full h-48 object-cover">
                        <div class="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center">
                            <svg class="w-8 h-8 text-white opacity-0 hover:opacity-100 transition-opacity duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                
                <div class="text-center mt-12">
                    <a href="gallery.php" class="bg-primary-500 hover:bg-primary-600 text-white px-8 py-3 rounded-lg font-semibold transition-colors">
                        <?php echo $lang === 'ar' ? 'عرض المزيد' : 'View More'; ?>
                    </a>
                </div>
            </div>
        </section>

        <!-- Blog Articles Section -->
        <section id="blog-preview" class="py-20 bg-gray-100">
            <div class="max-w-7xl mx-auto px-4">
                <div class="text-center mb-16">
                    <h2 class="text-4xl font-bold mb-4 text-gray-800">
                        <?php echo $lang === 'ar' ? 'أحدث المقالات' : 'Latest Articles'; ?>
                    </h2>
                    <p class="text-lg text-gray-600 max-w-2xl mx-auto">
                        <?php echo $lang === 'ar' ? 
                            'اقرأ أحدث المقالات والنصائح السياحية لتخطط لرحلتك القادمة بأفضل طريقة' : 
                            'Read the latest articles and travel tips to plan your next trip in the best way'; ?>
                    </p>
                </div>
                
                <div class="grid md:grid-cols-3 gap-8">
                    <?php foreach ($recentArticles as $article): ?>
                    <article class="article-card bg-white rounded-lg shadow-lg overflow-hidden transform hover:scale-105 transition-all duration-300">
                        <div class="relative overflow-hidden">
                            <img src="<?php echo $article['featured_image']; ?>" alt="<?php echo $article['title']; ?>" class="w-full h-48 object-cover hover:scale-110 transition-transform duration-300">
                            <div class="absolute top-4 left-4 bg-primary-500 text-white px-3 py-1 rounded-full text-sm">
                                <?php echo $article['category']; ?>
                            </div>
                        </div>
                        <div class="p-6">
                            <h3 class="text-xl font-bold mb-2 text-gray-800 line-clamp-2"><?php echo $article['title']; ?></h3>
                            <p class="text-gray-600 mb-4 line-clamp-3"><?php echo $article['excerpt']; ?></p>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-500"><?php echo date('M d, Y', strtotime($article['created_at'])); ?></span>
                                <a href="article.php?id=<?php echo $article['id']; ?>" class="text-primary-500 hover:text-primary-600 font-semibold text-sm">
                                    <?php echo $lang === 'ar' ? 'اقرأ المزيد' : 'Read More'; ?>
                                </a>
                            </div>
                        </div>
                    </article>
                    <?php endforeach; ?>
                </div>
                
                <div class="text-center mt-12">
                    <a href="blog.php" class="bg-primary-500 hover:bg-primary-600 text-white px-8 py-3 rounded-lg font-semibold transition-colors">
                        <?php echo $lang === 'ar' ? 'جميع المقالات' : 'All Articles'; ?>
                    </a>
                </div>
            </div>
        </section>

        <!-- Testimonials Section -->
        <section id="testimonials" class="py-20 bg-white">
            <div class="max-w-7xl mx-auto px-4">
                <div class="text-center mb-16">
                    <h2 class="text-4xl font-bold mb-4 text-gray-800">
                        <?php echo $lang === 'ar' ? 'آراء العملاء' : 'Customer Reviews'; ?>
                    </h2>
                    <p class="text-lg text-gray-600 max-w-2xl mx-auto">
                        <?php echo $lang === 'ar' ? 
                            'اقرأ تجارب عملائنا الرائعة وآرائهم حول خدماتنا المميزة' : 
                            'Read our customers\' wonderful experiences and opinions about our exceptional services'; ?>
                    </p>
                </div>
                
                <div class="testimonials-slider relative">
                    <div class="testimonials-container overflow-hidden">
                        <div class="testimonials-track flex transition-transform duration-500">
                            <?php foreach ($testimonials as $testimonial): ?>
                            <div class="testimonial-slide flex-shrink-0 w-full md:w-1/2 lg:w-1/3 px-4">
                                <div class="bg-gray-50 rounded-lg p-8 shadow-lg">
                                    <div class="flex items-center mb-4">
                                        <img src="<?php echo $testimonial['avatar']; ?>" alt="<?php echo $testimonial['name']; ?>" class="w-12 h-12 rounded-full mr-4">
                                        <div>
                                            <h4 class="font-bold text-gray-800"><?php echo $testimonial['name']; ?></h4>
                                            <div class="flex text-yellow-400">
                                                <?php for ($i = 0; $i < $testimonial['rating']; $i++): ?>
                                                <svg class="w-4 h-4 fill-current" viewBox="0 0 20 20">
                                                    <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/>
                                                </svg>
                                                <?php endfor; ?>
                                            </div>
                                        </div>
                                    </div>
                                    <p class="text-gray-600 italic">"<?php echo $testimonial['review']; ?>"</p>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    
                    <button class="testimonials-prev absolute left-4 top-1/2 transform -translate-y-1/2 bg-white shadow-lg rounded-full p-3 hover:bg-gray-50 transition-colors">
                        <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                    </button>
                    <button class="testimonials-next absolute right-4 top-1/2 transform -translate-y-1/2 bg-white shadow-lg rounded-full p-3 hover:bg-gray-50 transition-colors">
                        <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </section>

        <!-- WhatsApp CTA Section -->
        <section id="whatsapp-cta" class="py-20 bg-green-500">
            <div class="max-w-4xl mx-auto px-4 text-center text-white">
                <div class="mb-8">
                    <svg class="w-16 h-16 mx-auto mb-6" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 ************* 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
                    </svg>
                </div>
                <h2 class="text-4xl font-bold mb-4">
                    <?php echo $lang === 'ar' ? 'تواصل معنا الآن' : 'Contact Us Now'; ?>
                </h2>
                <p class="text-xl mb-8">
                    <?php echo $lang === 'ar' ? 
                        'احصل على استشارة مجانية وخطط لرحلتك القادمة مع خبرائنا' : 
                        'Get a free consultation and plan your next trip with our experts'; ?>
                </p>
                <a href="https://wa.me/60123456789" target="_blank" class="inline-flex items-center bg-white text-green-500 px-8 py-4 rounded-lg text-lg font-bold hover:bg-gray-100 transition-colors">
                    <svg class="w-6 h-6 mr-3" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 ************* 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
                    </svg>
                    <?php echo $lang === 'ar' ? 'تواصل عبر واتساب' : 'Chat on WhatsApp'; ?>
                </a>
            </div>
        </section>

        <!-- Newsletter Section -->
        <section id="newsletter" class="py-20 bg-gray-800 text-white">
            <div class="max-w-4xl mx-auto px-4 text-center">
                <h2 class="text-4xl font-bold mb-4">
                    <?php echo $lang === 'ar' ? 'اشترك في النشرة الإخبارية' : 'Subscribe to Newsletter'; ?>
                </h2>
                <p class="text-xl mb-8 text-gray-300">
                    <?php echo $lang === 'ar' ? 
                        'احصل على أحدث العروض والوجهات السياحية مباشرة في بريدك الإلكتروني' : 
                        'Get the latest offers and tourist destinations directly in your email'; ?>
                </p>
                
                <form id="newsletter-form" class="max-w-md mx-auto">
                    <div class="flex">
                        <input type="email" name="email" placeholder="<?php echo $lang === 'ar' ? 'أدخل بريدك الإلكتروني' : 'Enter your email'; ?>" class="flex-1 px-4 py-3 rounded-l-lg text-gray-800 focus:outline-none focus:ring-2 focus:ring-primary-500" required>
                        <button type="submit" class="bg-primary-500 hover:bg-primary-600 px-6 py-3 rounded-r-lg font-semibold transition-colors">
                            <?php echo $lang === 'ar' ? 'اشترك' : 'Subscribe'; ?>
                        </button>
                    </div>
                </form>
            </div>
        </section>
    </main>
    
    <?php include 'includes/footer.php'; ?>
    
    <!-- Lightbox Modal -->
    <div id="lightbox" class="fixed inset-0 bg-black bg-opacity-90 z-50 hidden items-center justify-center">
        <div class="relative max-w-4xl max-h-full p-4">
            <img id="lightbox-image" src="" alt="" class="max-w-full max-h-full object-contain">
            <button id="lightbox-close" class="absolute top-4 right-4 text-white text-4xl hover:text-gray-300">
                &times;
            </button>
        </div>
    </div>
    
    <!-- Custom JavaScript -->
    <script src="assets/js/main.js"></script>
</body>
</html>
