<?php
session_start();
require_once 'includes/config.php';

// Handle language switching
if (isset($_GET['lang']) && in_array($_GET['lang'], SUPPORTED_LANGS)) {
    $_SESSION['lang'] = $_GET['lang'];
}

$lang = $_SESSION['lang'] ?? DEFAULT_LANG;
$isRTL = $lang === 'ar';
?>

<!DOCTYPE html>
<html lang="<?php echo $lang; ?>" dir="<?php echo $isRTL ? 'rtl' : 'ltr'; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Animation Test</title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <?php if ($lang === 'ar'): ?>
        <link href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <?php else: ?>
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <?php endif; ?>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'arabic': ['IBM Plex Sans Arabic', 'sans-serif'],
                        'english': ['Inter', 'sans-serif']
                    },
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1'
                        }
                    }
                }
            }
        }
    </script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    
    <!-- GSAP for animations -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js"></script>
</head>
<body class="<?php echo $lang === 'ar' ? 'font-arabic' : 'font-english'; ?> bg-gray-50">
    
    <?php include 'includes/header.php'; ?>
    
    <main class="pt-16">
        <!-- Test Section 1 -->
        <section class="py-20 bg-white">
            <div class="max-w-4xl mx-auto px-4">
                <div class="text-center mb-12 scroll-animate">
                    <h1 class="text-4xl font-bold mb-4 text-gray-800">
                        <?php echo $lang === 'ar' ? 'اختبار الرسوم المتحركة' : 'Animation Test'; ?>
                    </h1>
                    <p class="text-lg text-gray-600">
                        <?php echo $lang === 'ar' ? 'هذه الصفحة لاختبار الرسوم المتحركة' : 'This page is for testing animations'; ?>
                    </p>
                </div>
                
                <div class="grid md:grid-cols-3 gap-8">
                    <div class="bg-gray-100 p-6 rounded-lg scroll-animate">
                        <h3 class="text-xl font-bold mb-4">
                            <?php echo $lang === 'ar' ? 'بطاقة 1' : 'Card 1'; ?>
                        </h3>
                        <p class="text-gray-600">
                            <?php echo $lang === 'ar' ? 'هذا نص تجريبي للبطاقة الأولى' : 'This is test content for the first card'; ?>
                        </p>
                    </div>
                    
                    <div class="bg-gray-100 p-6 rounded-lg scroll-animate">
                        <h3 class="text-xl font-bold mb-4">
                            <?php echo $lang === 'ar' ? 'بطاقة 2' : 'Card 2'; ?>
                        </h3>
                        <p class="text-gray-600">
                            <?php echo $lang === 'ar' ? 'هذا نص تجريبي للبطاقة الثانية' : 'This is test content for the second card'; ?>
                        </p>
                    </div>
                    
                    <div class="bg-gray-100 p-6 rounded-lg scroll-animate">
                        <h3 class="text-xl font-bold mb-4">
                            <?php echo $lang === 'ar' ? 'بطاقة 3' : 'Card 3'; ?>
                        </h3>
                        <p class="text-gray-600">
                            <?php echo $lang === 'ar' ? 'هذا نص تجريبي للبطاقة الثالثة' : 'This is test content for the third card'; ?>
                        </p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Test Section 2 - Counters -->
        <section class="py-20 bg-primary-600 text-white">
            <div class="max-w-4xl mx-auto px-4">
                <div class="text-center mb-12 scroll-animate">
                    <h2 class="text-3xl font-bold mb-4">
                        <?php echo $lang === 'ar' ? 'اختبار العدادات' : 'Counter Test'; ?>
                    </h2>
                </div>
                
                <div class="grid md:grid-cols-4 gap-8 text-center">
                    <div class="scroll-animate">
                        <div class="text-4xl font-bold mb-2 counter" data-target="100">0</div>
                        <div><?php echo $lang === 'ar' ? 'عميل' : 'Clients'; ?></div>
                    </div>
                    <div class="scroll-animate">
                        <div class="text-4xl font-bold mb-2 counter" data-target="50">0</div>
                        <div><?php echo $lang === 'ar' ? 'مشروع' : 'Projects'; ?></div>
                    </div>
                    <div class="scroll-animate">
                        <div class="text-4xl font-bold mb-2 counter" data-target="25">0</div>
                        <div><?php echo $lang === 'ar' ? 'جائزة' : 'Awards'; ?></div>
                    </div>
                    <div class="scroll-animate">
                        <div class="text-4xl font-bold mb-2 counter" data-target="5">0</div>
                        <div><?php echo $lang === 'ar' ? 'سنوات' : 'Years'; ?></div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Test Section 3 - More Content -->
        <section class="py-20 bg-gray-100">
            <div class="max-w-4xl mx-auto px-4">
                <div class="scroll-animate">
                    <h2 class="text-3xl font-bold mb-8 text-center text-gray-800">
                        <?php echo $lang === 'ar' ? 'المزيد من المحتوى' : 'More Content'; ?>
                    </h2>
                </div>
                
                <div class="space-y-8">
                    <div class="bg-white p-8 rounded-lg shadow-lg scroll-animate">
                        <h3 class="text-2xl font-bold mb-4 text-gray-800">
                            <?php echo $lang === 'ar' ? 'قسم تجريبي 1' : 'Test Section 1'; ?>
                        </h3>
                        <p class="text-gray-600 leading-relaxed">
                            <?php echo $lang === 'ar' ? 
                                'هذا نص تجريبي طويل لاختبار كيفية ظهور المحتوى مع الرسوم المتحركة. يجب أن يظهر هذا النص بوضوح دون أي مشاكل في الشفافية.' : 
                                'This is a long test text to check how content appears with animations. This text should appear clearly without any opacity issues.'; ?>
                        </p>
                    </div>
                    
                    <div class="bg-white p-8 rounded-lg shadow-lg scroll-animate">
                        <h3 class="text-2xl font-bold mb-4 text-gray-800">
                            <?php echo $lang === 'ar' ? 'قسم تجريبي 2' : 'Test Section 2'; ?>
                        </h3>
                        <p class="text-gray-600 leading-relaxed">
                            <?php echo $lang === 'ar' ? 
                                'هذا قسم آخر لاختبار الرسوم المتحركة. جميع النصوص يجب أن تكون مرئية وواضحة.' : 
                                'This is another section to test animations. All text should be visible and clear.'; ?>
                        </p>
                    </div>
                    
                    <div class="bg-white p-8 rounded-lg shadow-lg scroll-animate">
                        <h3 class="text-2xl font-bold mb-4 text-gray-800">
                            <?php echo $lang === 'ar' ? 'قسم تجريبي 3' : 'Test Section 3'; ?>
                        </h3>
                        <p class="text-gray-600 leading-relaxed">
                            <?php echo $lang === 'ar' ? 
                                'القسم الأخير للاختبار. إذا كنت تستطيع قراءة هذا النص بوضوح، فإن الرسوم المتحركة تعمل بشكل صحيح.' : 
                                'The final test section. If you can read this text clearly, the animations are working correctly.'; ?>
                        </p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Debug Info -->
        <section class="py-12 bg-yellow-50 border-t-4 border-yellow-400">
            <div class="max-w-4xl mx-auto px-4">
                <h3 class="text-lg font-bold mb-4 text-yellow-800">Debug Information:</h3>
                <div class="bg-white p-4 rounded border text-sm">
                    <p><strong>GSAP Loaded:</strong> <span id="gsap-status">Checking...</span></p>
                    <p><strong>ScrollTrigger Loaded:</strong> <span id="scrolltrigger-status">Checking...</span></p>
                    <p><strong>Animation Method:</strong> <span id="animation-method">Checking...</span></p>
                </div>
            </div>
        </section>
    </main>
    
    <?php include 'includes/footer.php'; ?>
    
    <!-- Custom JavaScript -->
    <script src="assets/js/main.js"></script>
    
    <script>
        // Debug information
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('gsap-status').textContent = typeof gsap !== 'undefined' ? 'Yes' : 'No';
            document.getElementById('scrolltrigger-status').textContent = typeof ScrollTrigger !== 'undefined' ? 'Yes' : 'No';
            document.getElementById('animation-method').textContent = typeof gsap !== 'undefined' ? 'GSAP' : 'Fallback CSS';
            
            // Force show all content after 2 seconds as fallback
            setTimeout(() => {
                const elements = document.querySelectorAll('.scroll-animate');
                elements.forEach(el => {
                    el.style.opacity = '1';
                    el.style.transform = 'translateY(0)';
                });
            }, 2000);
        });
    </script>
</body>
</html>
