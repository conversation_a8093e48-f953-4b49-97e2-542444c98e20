<?php
session_start();
require_once 'includes/config.php';
require_once 'includes/Database.php';
require_once 'includes/ContentManager.php';

// Handle language switching
if (isset($_GET['lang']) && in_array($_GET['lang'], SUPPORTED_LANGS)) {
    $_SESSION['lang'] = $_GET['lang'];
}

$db = new Database();
$contentManager = new ContentManager($db);

// Get current language
$lang = $_SESSION['lang'] ?? DEFAULT_LANG;
$isRTL = $lang === 'ar';
?>

<!DOCTYPE html>
<html lang="<?php echo $lang; ?>" dir="<?php echo $isRTL ? 'rtl' : 'ltr'; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $lang === 'ar' ? 'اتصل بنا - رحلات ماليزيا السياحية' : 'Contact Us - Malaysia Tourism Trips'; ?></title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <?php if ($lang === 'ar'): ?>
        <link href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <?php else: ?>
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <?php endif; ?>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'arabic': ['IBM Plex Sans Arabic', 'sans-serif'],
                        'english': ['Inter', 'sans-serif']
                    },
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1'
                        },
                        secondary: {
                            500: '#f59e0b',
                            600: '#d97706'
                        }
                    }
                }
            }
        }
    </script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">

    <!-- IMMEDIATE FIX: Force content visibility -->
    <style>
        .scroll-animate,
        .article-card,
        .destination-card,
        .gallery-item {
            opacity: 1 !important;
            transform: translateY(0) !important;
            visibility: visible !important;
        }
    </style>
    
    <!-- GSAP for animations -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js"></script>
</head>
<body class="<?php echo $lang === 'ar' ? 'font-arabic' : 'font-english'; ?> bg-gray-50">
    
    <?php include 'includes/header.php'; ?>
    
    <main class="pt-16">
        <!-- Hero Section -->
        <section class="relative h-96 bg-gradient-to-r from-primary-600 to-primary-800 overflow-hidden">
            <div class="absolute inset-0 bg-black bg-opacity-30"></div>
            <div class="relative z-10 flex items-center justify-center h-full text-white text-center">
                <div class="max-w-4xl mx-auto px-4">
                    <h1 class="text-5xl md:text-6xl font-bold mb-4 hero-title">
                        <?php echo $lang === 'ar' ? 'اتصل بنا' : 'Contact Us'; ?>
                    </h1>
                    <p class="text-xl md:text-2xl hero-subtitle">
                        <?php echo $lang === 'ar' ? 'نحن هنا لمساعدتك في تخطيط رحلتك المثالية' : 'We\'re here to help you plan your perfect trip'; ?>
                    </p>
                </div>
            </div>
        </section>

        <!-- Contact Form & Info Section -->
        <section class="py-20 bg-white">
            <div class="max-w-7xl mx-auto px-4">
                <div class="grid lg:grid-cols-2 gap-12">
                    <!-- Contact Form -->
                    <div class="scroll-animate">
                        <h2 class="text-3xl font-bold mb-6 text-gray-800">
                            <?php echo $lang === 'ar' ? 'أرسل لنا رسالة' : 'Send Us a Message'; ?>
                        </h2>
                        <p class="text-gray-600 mb-8">
                            <?php echo $lang === 'ar' ? 
                                'املأ النموذج أدناه وسنتواصل معك في أقرب وقت ممكن' : 
                                'Fill out the form below and we\'ll get back to you as soon as possible'; ?>
                        </p>
                        
                        <form id="contact-form" class="space-y-6">
                            <div class="grid md:grid-cols-2 gap-6">
                                <div>
                                    <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                                        <?php echo $lang === 'ar' ? 'الاسم الكامل' : 'Full Name'; ?> *
                                    </label>
                                    <input type="text" id="name" name="name" required 
                                           class="form-input" 
                                           placeholder="<?php echo $lang === 'ar' ? 'أدخل اسمك الكامل' : 'Enter your full name'; ?>">
                                </div>
                                <div>
                                    <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                                        <?php echo $lang === 'ar' ? 'البريد الإلكتروني' : 'Email Address'; ?> *
                                    </label>
                                    <input type="email" id="email" name="email" required 
                                           class="form-input" 
                                           placeholder="<?php echo $lang === 'ar' ? 'أدخل بريدك الإلكتروني' : 'Enter your email address'; ?>">
                                </div>
                            </div>
                            
                            <div class="grid md:grid-cols-2 gap-6">
                                <div>
                                    <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">
                                        <?php echo $lang === 'ar' ? 'رقم الهاتف' : 'Phone Number'; ?>
                                    </label>
                                    <input type="tel" id="phone" name="phone" 
                                           class="form-input" 
                                           placeholder="<?php echo $lang === 'ar' ? 'أدخل رقم هاتفك' : 'Enter your phone number'; ?>">
                                </div>
                                <div>
                                    <label for="subject" class="block text-sm font-medium text-gray-700 mb-2">
                                        <?php echo $lang === 'ar' ? 'الموضوع' : 'Subject'; ?>
                                    </label>
                                    <select id="subject" name="subject" class="form-input">
                                        <option value=""><?php echo $lang === 'ar' ? 'اختر الموضوع' : 'Select Subject'; ?></option>
                                        <option value="general"><?php echo $lang === 'ar' ? 'استفسار عام' : 'General Inquiry'; ?></option>
                                        <option value="booking"><?php echo $lang === 'ar' ? 'حجز رحلة' : 'Trip Booking'; ?></option>
                                        <option value="support"><?php echo $lang === 'ar' ? 'الدعم الفني' : 'Technical Support'; ?></option>
                                        <option value="partnership"><?php echo $lang === 'ar' ? 'شراكة' : 'Partnership'; ?></option>
                                    </select>
                                </div>
                            </div>
                            
                            <div>
                                <label for="message" class="block text-sm font-medium text-gray-700 mb-2">
                                    <?php echo $lang === 'ar' ? 'الرسالة' : 'Message'; ?> *
                                </label>
                                <textarea id="message" name="message" rows="6" required 
                                          class="form-textarea" 
                                          placeholder="<?php echo $lang === 'ar' ? 'اكتب رسالتك هنا...' : 'Write your message here...'; ?>"></textarea>
                            </div>
                            
                            <button type="submit" class="form-button w-full">
                                <span class="button-text">
                                    <?php echo $lang === 'ar' ? 'إرسال الرسالة' : 'Send Message'; ?>
                                </span>
                            </button>
                        </form>
                    </div>
                    
                    <!-- Contact Information -->
                    <div class="scroll-animate">
                        <h2 class="text-3xl font-bold mb-6 text-gray-800">
                            <?php echo $lang === 'ar' ? 'معلومات التواصل' : 'Contact Information'; ?>
                        </h2>
                        <p class="text-gray-600 mb-8">
                            <?php echo $lang === 'ar' ? 
                                'يمكنك التواصل معنا من خلال الطرق التالية' : 
                                'You can reach us through the following methods'; ?>
                        </p>
                        
                        <div class="space-y-6">
                            <!-- Phone -->
                            <div class="flex items-start space-x-4 <?php echo $isRTL ? 'space-x-reverse' : ''; ?>">
                                <div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center flex-shrink-0">
                                    <svg class="w-6 h-6 text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-800 mb-1">
                                        <?php echo $lang === 'ar' ? 'الهاتف' : 'Phone'; ?>
                                    </h3>
                                    <p class="text-gray-600">+60 12 345 6789</p>
                                    <p class="text-sm text-gray-500">
                                        <?php echo $lang === 'ar' ? 'متاح 24/7' : 'Available 24/7'; ?>
                                    </p>
                                </div>
                            </div>
                            
                            <!-- Email -->
                            <div class="flex items-start space-x-4 <?php echo $isRTL ? 'space-x-reverse' : ''; ?>">
                                <div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center flex-shrink-0">
                                    <svg class="w-6 h-6 text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-800 mb-1">
                                        <?php echo $lang === 'ar' ? 'البريد الإلكتروني' : 'Email'; ?>
                                    </h3>
                                    <p class="text-gray-600"><EMAIL></p>
                                    <p class="text-sm text-gray-500">
                                        <?php echo $lang === 'ar' ? 'نرد خلال 24 ساعة' : 'We reply within 24 hours'; ?>
                                    </p>
                                </div>
                            </div>
                            
                            <!-- Address -->
                            <div class="flex items-start space-x-4 <?php echo $isRTL ? 'space-x-reverse' : ''; ?>">
                                <div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center flex-shrink-0">
                                    <svg class="w-6 h-6 text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-800 mb-1">
                                        <?php echo $lang === 'ar' ? 'العنوان' : 'Address'; ?>
                                    </h3>
                                    <p class="text-gray-600">
                                        <?php echo $lang === 'ar' ? 'كوالالمبور، ماليزيا' : 'Kuala Lumpur, Malaysia'; ?>
                                    </p>
                                    <p class="text-sm text-gray-500">
                                        <?php echo $lang === 'ar' ? 'مكتبنا الرئيسي' : 'Our main office'; ?>
                                    </p>
                                </div>
                            </div>
                            
                            <!-- WhatsApp -->
                            <div class="flex items-start space-x-4 <?php echo $isRTL ? 'space-x-reverse' : ''; ?>">
                                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0">
                                    <svg class="w-6 h-6 text-green-500" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 ************* 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-800 mb-1">WhatsApp</h3>
                                    <p class="text-gray-600">+60 12 345 6789</p>
                                    <a href="https://wa.me/<?php echo WHATSAPP_NUMBER; ?>" target="_blank" 
                                       class="inline-flex items-center text-green-500 hover:text-green-600 text-sm font-medium">
                                        <?php echo $lang === 'ar' ? 'ابدأ المحادثة' : 'Start Chat'; ?>
                                        <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                                        </svg>
                                    </a>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Social Media -->
                        <div class="mt-8 pt-8 border-t border-gray-200">
                            <h3 class="text-lg font-semibold text-gray-800 mb-4">
                                <?php echo $lang === 'ar' ? 'تابعنا على' : 'Follow Us'; ?>
                            </h3>
                            <div class="flex space-x-4 <?php echo $isRTL ? 'space-x-reverse' : ''; ?>">
                                <a href="https://facebook.com/malaysiaturism" target="_blank" 
                                   class="w-10 h-10 bg-blue-600 hover:bg-blue-700 text-white rounded-lg flex items-center justify-center transition-colors">
                                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                                    </svg>
                                </a>
                                <a href="https://instagram.com/malaysiaturism" target="_blank" 
                                   class="w-10 h-10 bg-pink-600 hover:bg-pink-700 text-white rounded-lg flex items-center justify-center transition-colors">
                                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                                    </svg>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Map Section -->
        <section class="py-20 bg-gray-100">
            <div class="max-w-7xl mx-auto px-4">
                <div class="text-center mb-12 scroll-animate">
                    <h2 class="text-3xl font-bold mb-4 text-gray-800">
                        <?php echo $lang === 'ar' ? 'موقعنا' : 'Our Location'; ?>
                    </h2>
                    <p class="text-lg text-gray-600">
                        <?php echo $lang === 'ar' ? 'زورنا في مكتبنا الرئيسي في كوالالمبور' : 'Visit us at our main office in Kuala Lumpur'; ?>
                    </p>
                </div>

                <div class="bg-white rounded-lg shadow-lg overflow-hidden scroll-animate">
                    <div class="h-96 bg-gray-200 relative">
                        <!-- Google Maps Embed -->
                        <iframe
                            src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3983.7730573109!2d101.68685931475394!3d3.1516964975285!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x31cc362abd08e7d3%3A0x232e1ff540d86c99!2sPetronas%20Twin%20Towers!5e0!3m2!1sen!2smy!4v1635000000000!5m2!1sen!2smy"
                            width="100%"
                            height="100%"
                            style="border:0;"
                            allowfullscreen=""
                            loading="lazy"
                            referrerpolicy="no-referrer-when-downgrade">
                        </iframe>

                        <!-- Map Overlay -->
                        <div class="absolute top-4 <?php echo $isRTL ? 'right-4' : 'left-4'; ?> bg-white rounded-lg shadow-lg p-4 max-w-xs">
                            <h3 class="font-bold text-gray-800 mb-2">
                                <?php echo $lang === 'ar' ? 'مكتب رحلات ماليزيا' : 'Malaysia Tourism Office'; ?>
                            </h3>
                            <p class="text-sm text-gray-600 mb-2">
                                <?php echo $lang === 'ar' ? 'كوالالمبور، ماليزيا' : 'Kuala Lumpur, Malaysia'; ?>
                            </p>
                            <div class="flex items-center text-sm text-primary-500">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                                <?php echo $lang === 'ar' ? 'عرض الاتجاهات' : 'Get Directions'; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- CTA Section -->
        <section class="py-20 bg-primary-600 text-white">
            <div class="max-w-4xl mx-auto px-4 text-center">
                <h2 class="text-4xl font-bold mb-6">
                    <?php echo $lang === 'ar' ? 'جاهز لبدء رحلتك؟' : 'Ready to Start Your Journey?'; ?>
                </h2>
                <p class="text-xl mb-8 opacity-90">
                    <?php echo $lang === 'ar' ?
                        'تواصل معنا الآن واحصل على استشارة مجانية لتخطيط رحلتك المثالية' :
                        'Contact us now and get a free consultation to plan your perfect trip'; ?>
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="https://wa.me/<?php echo WHATSAPP_NUMBER; ?>" target="_blank"
                       class="bg-green-500 hover:bg-green-600 text-white px-8 py-3 rounded-lg font-semibold transition-colors flex items-center justify-center">
                        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 ************* 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
                        </svg>
                        <?php echo $lang === 'ar' ? 'تواصل عبر واتساب' : 'Chat on WhatsApp'; ?>
                    </a>
                    <a href="tel:+60123456789"
                       class="bg-white text-primary-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors flex items-center justify-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                        </svg>
                        <?php echo $lang === 'ar' ? 'اتصل بنا' : 'Call Us'; ?>
                    </a>
                </div>
            </div>
        </section>
    </main>

    <?php include 'includes/footer.php'; ?>

    <!-- Custom JavaScript -->
    <script src="assets/js/main.js"></script>

    <script>
        // Contact form handling
        document.getElementById('contact-form').addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const submitBtn = this.querySelector('button[type="submit"]');
            const buttonText = submitBtn.querySelector('.button-text');
            const originalText = buttonText.textContent;

            // Show loading state
            submitBtn.disabled = true;
            buttonText.innerHTML = '<div class="spinner"></div>' + '<?php echo $lang === "ar" ? "جاري الإرسال..." : "Sending..."; ?>';

            try {
                const response = await fetch('api/contact.php', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    alert('<?php echo $lang === "ar" ? "تم إرسال رسالتك بنجاح!" : "Your message has been sent successfully!"; ?>');
                    this.reset();
                } else {
                    alert(result.message || '<?php echo $lang === "ar" ? "حدث خطأ، يرجى المحاولة مرة أخرى" : "An error occurred, please try again"; ?>');
                }
            } catch (error) {
                console.error('Contact form error:', error);
                alert('<?php echo $lang === "ar" ? "حدث خطأ، يرجى المحاولة مرة أخرى" : "An error occurred, please try again"; ?>');
            } finally {
                submitBtn.disabled = false;
                buttonText.textContent = originalText;
            }
        });
    </script>
</body>
</html>
