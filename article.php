<?php
session_start();
require_once 'includes/config.php';
require_once 'includes/Database.php';
require_once 'includes/ContentManager.php';

// Handle language switching
if (isset($_GET['lang']) && in_array($_GET['lang'], SUPPORTED_LANGS)) {
    $_SESSION['lang'] = $_GET['lang'];
}

$db = new Database();
$contentManager = new ContentManager($db);

// Get current language
$lang = $_SESSION['lang'] ?? DEFAULT_LANG;
$isRTL = $lang === 'ar';

// Get article slug
$slug = $_GET['slug'] ?? '';

if (empty($slug)) {
    header('Location: blog.php?lang=' . $lang);
    exit;
}

// Get article by slug
$article = $contentManager->getArticleBySlug($slug, $lang);

if (!$article) {
    header('HTTP/1.0 404 Not Found');
    include '404.php';
    exit;
}

// Increment article views
$contentManager->incrementArticleViews($article['id']);

// Get related articles
$relatedArticles = $contentManager->getRecentArticles($lang, 3);
?>

<!DOCTYPE html>
<html lang="<?php echo $lang; ?>" dir="<?php echo $isRTL ? 'rtl' : 'ltr'; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($article['meta_title'] ?? $article['title']); ?> - <?php echo $lang === 'ar' ? 'رحلات ماليزيا السياحية' : 'Malaysia Tourism Trips'; ?></title>
    
    <!-- Meta Tags -->
    <meta name="description" content="<?php echo htmlspecialchars($article['meta_description'] ?? $article['excerpt']); ?>">
    <meta name="keywords" content="<?php echo htmlspecialchars($article['tags'] ?? ''); ?>">
    
    <!-- Open Graph -->
    <meta property="og:title" content="<?php echo htmlspecialchars($article['title']); ?>">
    <meta property="og:description" content="<?php echo htmlspecialchars($article['excerpt']); ?>">
    <meta property="og:image" content="<?php echo SITE_URL . '/' . ($article['featured_image'] ?? 'assets/images/articles/default.jpg'); ?>">
    <meta property="og:url" content="<?php echo SITE_URL . '/article.php?slug=' . $slug . '&lang=' . $lang; ?>">
    <meta property="og:type" content="article">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <?php if ($lang === 'ar'): ?>
        <link href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <?php else: ?>
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <?php endif; ?>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'arabic': ['IBM Plex Sans Arabic', 'sans-serif'],
                        'english': ['Inter', 'sans-serif']
                    },
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1'
                        },
                        secondary: {
                            500: '#f59e0b',
                            600: '#d97706'
                        }
                    }
                }
            }
        }
    </script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">

    <!-- IMMEDIATE FIX: Force content visibility -->
    <style>
        .scroll-animate,
        .article-card,
        .destination-card,
        .gallery-item {
            opacity: 1 !important;
            transform: translateY(0) !important;
            visibility: visible !important;
        }
    </style>
    
    <!-- GSAP for animations -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js"></script>
</head>
<body class="<?php echo $lang === 'ar' ? 'font-arabic' : 'font-english'; ?> bg-gray-50">
    
    <?php include 'includes/header.php'; ?>
    
    <main class="pt-16">
        <!-- Breadcrumb -->
        <section class="py-6 bg-gray-100">
            <div class="max-w-4xl mx-auto px-4">
                <nav class="flex items-center space-x-2 text-sm text-gray-600 <?php echo $isRTL ? 'space-x-reverse' : ''; ?>">
                    <a href="index.php?lang=<?php echo $lang; ?>" class="hover:text-primary-500">
                        <?php echo $lang === 'ar' ? 'الرئيسية' : 'Home'; ?>
                    </a>
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                    <a href="blog.php?lang=<?php echo $lang; ?>" class="hover:text-primary-500">
                        <?php echo $lang === 'ar' ? 'المدونة' : 'Blog'; ?>
                    </a>
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                    <span class="text-gray-800 font-medium"><?php echo htmlspecialchars($article['title']); ?></span>
                </nav>
            </div>
        </section>

        <!-- Article Content -->
        <article class="py-12 bg-white">
            <div class="max-w-4xl mx-auto px-4">
                <!-- Article Header -->
                <header class="mb-8 scroll-animate">
                    <?php if (!empty($article['category'])): ?>
                        <span class="inline-block px-3 py-1 bg-primary-100 text-primary-600 text-sm font-semibold rounded-full mb-4">
                            <?php echo $article['category']; ?>
                        </span>
                    <?php endif; ?>
                    
                    <h1 class="text-4xl md:text-5xl font-bold text-gray-800 mb-6 leading-tight">
                        <?php echo $article['title']; ?>
                    </h1>
                    
                    <div class="flex items-center justify-between text-gray-600 mb-6">
                        <div class="flex items-center space-x-4 <?php echo $isRTL ? 'space-x-reverse' : ''; ?>">
                            <span><?php echo formatDate($article['created_at'], $lang); ?></span>
                            <span>•</span>
                            <span><?php echo $article['views']; ?> <?php echo $lang === 'ar' ? 'مشاهدة' : 'views'; ?></span>
                        </div>
                        
                        <!-- Share Buttons -->
                        <div class="flex items-center space-x-3 <?php echo $isRTL ? 'space-x-reverse' : ''; ?>">
                            <span class="text-sm font-medium"><?php echo $lang === 'ar' ? 'شارك:' : 'Share:'; ?></span>
                            <a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo urlencode(SITE_URL . '/article.php?slug=' . $slug . '&lang=' . $lang); ?>" 
                               target="_blank" 
                               class="w-8 h-8 bg-blue-600 hover:bg-blue-700 text-white rounded-full flex items-center justify-center transition-colors">
                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                                </svg>
                            </a>
                            <a href="https://twitter.com/intent/tweet?url=<?php echo urlencode(SITE_URL . '/article.php?slug=' . $slug . '&lang=' . $lang); ?>&text=<?php echo urlencode($article['title']); ?>" 
                               target="_blank" 
                               class="w-8 h-8 bg-blue-400 hover:bg-blue-500 text-white rounded-full flex items-center justify-center transition-colors">
                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                                </svg>
                            </a>
                            <a href="https://wa.me/?text=<?php echo urlencode($article['title'] . ' - ' . SITE_URL . '/article.php?slug=' . $slug . '&lang=' . $lang); ?>" 
                               target="_blank" 
                               class="w-8 h-8 bg-green-500 hover:bg-green-600 text-white rounded-full flex items-center justify-center transition-colors">
                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
                                </svg>
                            </a>
                        </div>
                    </div>
                </header>

                <!-- Featured Image -->
                <?php if (!empty($article['featured_image'])): ?>
                    <div class="mb-8 scroll-animate">
                        <img src="<?php echo $article['featured_image']; ?>" 
                             alt="<?php echo htmlspecialchars($article['title']); ?>"
                             class="w-full h-96 object-cover rounded-lg shadow-lg">
                    </div>
                <?php endif; ?>

                <!-- Article Content -->
                <div class="prose prose-lg max-w-none scroll-animate">
                    <?php if (!empty($article['excerpt'])): ?>
                        <div class="text-xl text-gray-600 mb-8 p-6 bg-gray-50 rounded-lg border-l-4 border-primary-500">
                            <?php echo $article['excerpt']; ?>
                        </div>
                    <?php endif; ?>
                    
                    <div class="article-content text-gray-800 leading-relaxed">
                        <?php echo nl2br($article['content']); ?>
                    </div>
                </div>

                <!-- Tags -->
                <?php if (!empty($article['tags'])): ?>
                    <div class="mt-8 pt-8 border-t border-gray-200 scroll-animate">
                        <h3 class="text-lg font-semibold mb-4 text-gray-800">
                            <?php echo $lang === 'ar' ? 'الكلمات المفتاحية:' : 'Tags:'; ?>
                        </h3>
                        <div class="flex flex-wrap gap-2">
                            <?php 
                            $tags = explode(',', $article['tags']);
                            foreach ($tags as $tag): 
                                $tag = trim($tag);
                                if (!empty($tag)):
                            ?>
                                <span class="px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full hover:bg-primary-100 hover:text-primary-600 transition-colors cursor-pointer">
                                    #<?php echo $tag; ?>
                                </span>
                            <?php 
                                endif;
                            endforeach; 
                            ?>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </article>

        <!-- Related Articles -->
        <?php if (!empty($relatedArticles)): ?>
        <section class="py-16 bg-gray-100">
            <div class="max-w-7xl mx-auto px-4">
                <div class="text-center mb-12 scroll-animate">
                    <h2 class="text-3xl font-bold mb-4 text-gray-800">
                        <?php echo $lang === 'ar' ? 'مقالات ذات صلة' : 'Related Articles'; ?>
                    </h2>
                    <p class="text-lg text-gray-600">
                        <?php echo $lang === 'ar' ? 'اكتشف المزيد من المقالات المثيرة' : 'Discover more interesting articles'; ?>
                    </p>
                </div>
                
                <div class="grid md:grid-cols-3 gap-8">
                    <?php foreach ($relatedArticles as $relatedArticle): ?>
                        <?php if ($relatedArticle['id'] != $article['id']): ?>
                            <article class="bg-white rounded-lg shadow-lg overflow-hidden article-card scroll-animate">
                                <div class="aspect-w-16 aspect-h-9">
                                    <img src="<?php echo $relatedArticle['featured_image'] ?? 'assets/images/articles/default.jpg'; ?>" 
                                         alt="<?php echo htmlspecialchars($relatedArticle['title']); ?>"
                                         class="w-full h-48 object-cover">
                                </div>
                                <div class="p-6">
                                    <h3 class="text-xl font-bold mb-3 text-gray-800 line-clamp-2">
                                        <a href="article.php?slug=<?php echo $relatedArticle['slug']; ?>&lang=<?php echo $lang; ?>" 
                                           class="hover:text-primary-500 transition-colors">
                                            <?php echo $relatedArticle['title']; ?>
                                        </a>
                                    </h3>
                                    <p class="text-gray-600 mb-4 line-clamp-3">
                                        <?php echo $relatedArticle['excerpt']; ?>
                                    </p>
                                    <a href="article.php?slug=<?php echo $relatedArticle['slug']; ?>&lang=<?php echo $lang; ?>" 
                                       class="inline-flex items-center text-primary-500 hover:text-primary-600 font-semibold">
                                        <?php echo $lang === 'ar' ? 'اقرأ المزيد' : 'Read More'; ?>
                                        <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                        </svg>
                                    </a>
                                </div>
                            </article>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </div>
            </div>
        </section>
        <?php endif; ?>

        <!-- CTA Section -->
        <section class="py-20 bg-primary-600 text-white">
            <div class="max-w-4xl mx-auto px-4 text-center">
                <h2 class="text-4xl font-bold mb-6">
                    <?php echo $lang === 'ar' ? 'جاهز لبدء رحلتك؟' : 'Ready to Start Your Journey?'; ?>
                </h2>
                <p class="text-xl mb-8 opacity-90">
                    <?php echo $lang === 'ar' ? 
                        'تواصل معنا الآن لتخطيط رحلتك المثالية إلى ماليزيا' : 
                        'Contact us now to plan your perfect trip to Malaysia'; ?>
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="contact.php?lang=<?php echo $lang; ?>" 
                       class="bg-white text-primary-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                        <?php echo $lang === 'ar' ? 'تواصل معنا' : 'Contact Us'; ?>
                    </a>
                    <a href="https://wa.me/<?php echo WHATSAPP_NUMBER; ?>" target="_blank" 
                       class="bg-green-500 hover:bg-green-600 text-white px-8 py-3 rounded-lg font-semibold transition-colors flex items-center justify-center">
                        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
                        </svg>
                        <?php echo $lang === 'ar' ? 'واتساب' : 'WhatsApp'; ?>
                    </a>
                </div>
            </div>
        </section>
    </main>
    
    <?php include 'includes/footer.php'; ?>
    
    <!-- Custom JavaScript -->
    <script src="assets/js/main.js"></script>
</body>
</html>
