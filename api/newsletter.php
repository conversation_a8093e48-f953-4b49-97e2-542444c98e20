<?php
header('Content-Type: application/json');
require_once '../includes/config.php';
require_once '../includes/Database.php';
require_once '../includes/ContentManager.php';

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    $db = new Database();
    $contentManager = new ContentManager($db);
    
    // Get and sanitize email
    $email = sanitizeInput($_POST['email'] ?? '');
    
    // Validation
    if (empty($email)) {
        echo json_encode(['success' => false, 'message' => 'Email is required']);
        exit;
    }
    
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        echo json_encode(['success' => false, 'message' => 'Invalid email format']);
        exit;
    }
    
    // Subscribe to newsletter
    $result = $contentManager->subscribeNewsletter($email);
    
    if ($result) {
        echo json_encode([
            'success' => true,
            'message' => 'Successfully subscribed to newsletter!'
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Failed to subscribe. Please try again.'
        ]);
    }
    
} catch (Exception $e) {
    error_log('Newsletter subscription error: ' . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'An error occurred. Please try again later.'
    ]);
}
?>
