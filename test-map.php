<?php
session_start();
require_once 'includes/config.php';

$lang = $_SESSION['lang'] ?? DEFAULT_LANG;
$isRTL = $lang === 'ar';
?>

<!DOCTYPE html>
<html lang="<?php echo $lang; ?>" dir="<?php echo $isRTL ? 'rtl' : 'ltr'; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Malaysia Map Test</title>
    
    <!-- Fonts -->
    <?php if ($lang === 'ar'): ?>
        <link href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <?php else: ?>
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <?php endif; ?>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <style>
        .font-arabic { font-family: 'IBM Plex Sans Arabic', sans-serif; }
        .font-english { font-family: 'Inter', sans-serif; }
        
        .map-state {
            fill: #e5e7eb;
            stroke: #ffffff;
            stroke-width: 2;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .map-state:hover {
            fill: #0ea5e9;
            stroke: #0284c7;
            stroke-width: 3;
        }
        
        .map-state.active {
            fill: #0284c7;
            stroke: #0369a1;
            stroke-width: 3;
        }
    </style>
</head>
<body class="<?php echo $lang === 'ar' ? 'font-arabic' : 'font-english'; ?> bg-gray-50 p-8">
    
    <div class="max-w-6xl mx-auto">
        <h1 class="text-3xl font-bold text-center mb-8 text-blue-600">
            🗺️ <?php echo $lang === 'ar' ? 'اختبار خريطة ماليزيا التفاعلية' : 'Interactive Malaysia Map Test'; ?>
        </h1>
        
        <div class="grid lg:grid-cols-2 gap-8">
            <!-- Map -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-bold mb-4 text-center">
                    <?php echo $lang === 'ar' ? 'خريطة ماليزيا' : 'Malaysia Map'; ?>
                </h2>
                
                <div class="flex justify-center">
                    <svg class="malaysia-map" width="500" height="350" viewBox="0 0 600 400" xmlns="http://www.w3.org/2000/svg">
                        <!-- Peninsular Malaysia -->
                        
                        <!-- Perlis -->
                        <path id="perlis" class="map-state" data-state="perlis" 
                              d="M 180 80 L 200 75 L 210 85 L 205 95 L 185 90 Z" />
                        
                        <!-- Kedah -->
                        <path id="kedah" class="map-state" data-state="kedah" 
                              d="M 170 90 L 210 85 L 220 110 L 200 130 L 175 125 L 165 105 Z" />
                        
                        <!-- Penang -->
                        <circle id="penang" class="map-state" data-state="penang" 
                                cx="160" cy="120" r="8" />
                        
                        <!-- Perak -->
                        <path id="perak" class="map-state" data-state="perak" 
                              d="M 175 125 L 220 110 L 240 140 L 235 180 L 200 185 L 180 160 Z" />
                        
                        <!-- Selangor -->
                        <path id="selangor" class="map-state" data-state="selangor" 
                              d="M 200 185 L 235 180 L 245 200 L 230 220 L 210 215 Z" />
                        
                        <!-- Kuala Lumpur -->
                        <circle id="kuala-lumpur" class="map-state" data-state="kuala-lumpur" 
                                cx="220" cy="200" r="6" />
                        
                        <!-- Putrajaya -->
                        <circle id="putrajaya" class="map-state" data-state="putrajaya" 
                                cx="225" cy="210" r="4" />
                        
                        <!-- Negeri Sembilan -->
                        <path id="negeri-sembilan" class="map-state" data-state="negeri-sembilan" 
                              d="M 210 215 L 245 200 L 255 225 L 240 240 L 220 235 Z" />
                        
                        <!-- Melaka -->
                        <path id="melaka" class="map-state" data-state="melaka" 
                              d="M 220 235 L 240 240 L 245 255 L 230 260 L 215 250 Z" />
                        
                        <!-- Johor -->
                        <path id="johor" class="map-state" data-state="johor" 
                              d="M 215 250 L 255 225 L 280 240 L 290 270 L 270 290 L 240 285 L 220 270 Z" />
                        
                        <!-- Pahang -->
                        <path id="pahang" class="map-state" data-state="pahang" 
                              d="M 240 140 L 280 135 L 320 150 L 330 180 L 310 210 L 280 240 L 255 225 L 245 200 L 235 180 Z" />
                        
                        <!-- Terengganu -->
                        <path id="terengganu" class="map-state" data-state="terengganu" 
                              d="M 280 135 L 320 130 L 340 140 L 350 170 L 330 180 L 320 150 Z" />
                        
                        <!-- Kelantan -->
                        <path id="kelantan" class="map-state" data-state="kelantan" 
                              d="M 220 110 L 280 105 L 320 130 L 280 135 L 240 140 Z" />
                        
                        <!-- East Malaysia -->
                        
                        <!-- Sabah -->
                        <path id="sabah" class="map-state" data-state="sabah" 
                              d="M 420 120 L 480 115 L 520 130 L 530 160 L 510 180 L 470 175 L 430 165 L 410 145 Z" />
                        
                        <!-- Sarawak -->
                        <path id="sarawak" class="map-state" data-state="sarawak" 
                              d="M 380 180 L 470 175 L 510 180 L 520 210 L 500 240 L 450 245 L 400 240 L 370 220 L 360 200 Z" />
                        
                        <!-- Labuan -->
                        <circle id="labuan" class="map-state" data-state="labuan" 
                                cx="440" cy="150" r="4" />
                        
                        <!-- State Labels -->
                        <text x="190" y="85" text-anchor="middle" class="text-xs font-semibold fill-gray-600">Perlis</text>
                        <text x="190" y="110" text-anchor="middle" class="text-xs font-semibold fill-gray-600">Kedah</text>
                        <text x="160" y="135" text-anchor="middle" class="text-xs font-semibold fill-gray-600">Penang</text>
                        <text x="200" y="155" text-anchor="middle" class="text-xs font-semibold fill-gray-600">Perak</text>
                        <text x="220" y="200" text-anchor="middle" class="text-xs font-semibold fill-gray-600">Selangor</text>
                        <text x="220" y="190" text-anchor="middle" class="text-xs font-semibold fill-gray-600">KL</text>
                        <text x="235" y="230" text-anchor="middle" class="text-xs font-semibold fill-gray-600">N.Sembilan</text>
                        <text x="230" y="255" text-anchor="middle" class="text-xs font-semibold fill-gray-600">Melaka</text>
                        <text x="250" y="270" text-anchor="middle" class="text-xs font-semibold fill-gray-600">Johor</text>
                        <text x="280" y="180" text-anchor="middle" class="text-xs font-semibold fill-gray-600">Pahang</text>
                        <text x="330" y="155" text-anchor="middle" class="text-xs font-semibold fill-gray-600">Terengganu</text>
                        <text x="250" y="120" text-anchor="middle" class="text-xs font-semibold fill-gray-600">Kelantan</text>
                        <text x="470" y="150" text-anchor="middle" class="text-xs font-semibold fill-gray-600">Sabah</text>
                        <text x="440" y="215" text-anchor="middle" class="text-xs font-semibold fill-gray-600">Sarawak</text>
                    </svg>
                </div>
                
                <div class="mt-4 text-center text-sm text-gray-600">
                    <?php echo $lang === 'ar' ? 'انقر على أي ولاية لرؤية المعلومات' : 'Click on any state to see information'; ?>
                </div>
            </div>
            
            <!-- Information Panel -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <div id="info-panel">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold text-gray-800 mb-4">
                            <?php echo $lang === 'ar' ? 'اختر ولاية من الخريطة' : 'Select a state from the map'; ?>
                        </h3>
                        <p class="text-gray-600">
                            <?php echo $lang === 'ar' ? 
                                'انقر على أي ولاية في الخريطة لمعرفة أهم المعالم السياحية فيها.' : 
                                'Click on any state in the map to learn about its top tourist attractions.'; ?>
                        </p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Test Results -->
        <div class="mt-8 bg-green-100 border border-green-400 text-green-700 px-6 py-4 rounded-lg">
            <div class="flex items-center">
                <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <div>
                    <h4 class="font-bold">
                        <?php echo $lang === 'ar' ? 'اختبار الخريطة التفاعلية' : 'Interactive Map Test'; ?>
                    </h4>
                    <p class="text-sm">
                        <?php echo $lang === 'ar' ? 
                            'إذا كانت الخريطة تعمل بشكل صحيح، يجب أن تتغير الألوان عند التمرير والنقر.' : 
                            'If the map is working correctly, colors should change on hover and click.'; ?>
                    </p>
                </div>
            </div>
        </div>
        
        <!-- Navigation -->
        <div class="mt-8 text-center">
            <div class="flex flex-wrap justify-center gap-4">
                <a href="malaysia-map.php?lang=<?php echo $lang; ?>" class="bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600 transition-colors">
                    <?php echo $lang === 'ar' ? 'الخريطة الكاملة' : 'Full Map Page'; ?>
                </a>
                <a href="index.php?lang=<?php echo $lang; ?>" class="bg-gray-500 text-white px-6 py-3 rounded-lg hover:bg-gray-600 transition-colors">
                    <?php echo $lang === 'ar' ? 'الصفحة الرئيسية' : 'Homepage'; ?>
                </a>
                <a href="?lang=<?php echo $lang === 'ar' ? 'en' : 'ar'; ?>" class="bg-green-500 text-white px-6 py-3 rounded-lg hover:bg-green-600 transition-colors">
                    <?php echo $lang === 'ar' ? 'English' : 'العربية'; ?>
                </a>
            </div>
        </div>
    </div>
    
    <script>
        // Simple test data
        const testData = {
            'kuala-lumpur': {
                name: '<?php echo $lang === "ar" ? "كوالالمبور" : "Kuala Lumpur"; ?>',
                info: '<?php echo $lang === "ar" ? "العاصمة الحديثة مع برجي بتروناس" : "Modern capital with Petronas Towers"; ?>'
            },
            'penang': {
                name: '<?php echo $lang === "ar" ? "بينانغ" : "Penang"; ?>',
                info: '<?php echo $lang === "ar" ? "لؤلؤة الشرق مع التراث العالمي" : "Pearl of the Orient with World Heritage"; ?>'
            },
            'sabah': {
                name: '<?php echo $lang === "ar" ? "صباح" : "Sabah"; ?>',
                info: '<?php echo $lang === "ar" ? "أرض تحت الرياح مع جبل كينابالو" : "Land Below the Wind with Mount Kinabalu"; ?>'
            },
            'sarawak': {
                name: '<?php echo $lang === "ar" ? "ساراواك" : "Sarawak"; ?>',
                info: '<?php echo $lang === "ar" ? "أرض القرود مع كهوف مولو" : "Land of Hornbills with Mulu Caves"; ?>'
            }
        };
        
        document.addEventListener('DOMContentLoaded', function() {
            const mapStates = document.querySelectorAll('.map-state');
            const infoPanel = document.getElementById('info-panel');
            
            mapStates.forEach(state => {
                state.addEventListener('click', function() {
                    const stateId = this.getAttribute('data-state');
                    
                    // Remove active from all states
                    mapStates.forEach(s => s.classList.remove('active'));
                    
                    // Add active to clicked state
                    this.classList.add('active');
                    
                    // Show info
                    if (testData[stateId]) {
                        infoPanel.innerHTML = `
                            <div class="text-center">
                                <h3 class="text-2xl font-bold text-blue-600 mb-4">${testData[stateId].name}</h3>
                                <p class="text-gray-700 mb-4">${testData[stateId].info}</p>
                                <div class="bg-blue-50 p-4 rounded-lg">
                                    <p class="text-sm text-blue-600">
                                        ✅ <?php echo $lang === "ar" ? "النقر يعمل بشكل صحيح!" : "Click functionality working!"; ?>
                                    </p>
                                </div>
                            </div>
                        `;
                    } else {
                        infoPanel.innerHTML = `
                            <div class="text-center">
                                <h3 class="text-xl font-bold text-gray-800 mb-4">${stateId}</h3>
                                <p class="text-gray-600">
                                    <?php echo $lang === "ar" ? "معلومات قريباً" : "Information coming soon"; ?>
                                </p>
                            </div>
                        `;
                    }
                });
            });
            
            console.log('✅ Map test loaded successfully');
            console.log('✅ Found', mapStates.length, 'clickable states');
        });
    </script>
</body>
</html>
