<?php
session_start();
require_once 'includes/config.php';

echo "<h2>Language Debug Information</h2>";

echo "<h3>1. Configuration Check:</h3>";
echo "DEFAULT_LANG: " . DEFAULT_LANG . "<br>";
echo "SUPPORTED_LANGS: " . implode(', ', SUPPORTED_LANGS) . "<br>";

echo "<h3>2. URL Parameters:</h3>";
echo "GET lang: " . ($_GET['lang'] ?? 'Not set') . "<br>";

echo "<h3>3. Session Check:</h3>";
echo "Session lang before: " . ($_SESSION['lang'] ?? 'Not set') . "<br>";

// Handle language switching
if (isset($_GET['lang']) && in_array($_GET['lang'], SUPPORTED_LANGS)) {
    $_SESSION['lang'] = $_GET['lang'];
    echo "Language set to: " . $_GET['lang'] . "<br>";
}

echo "Session lang after: " . ($_SESSION['lang'] ?? 'Not set') . "<br>";

$lang = $_SESSION['lang'] ?? DEFAULT_LANG;
$isRTL = $lang === 'ar';

echo "<h3>4. Final Values:</h3>";
echo "Current lang: " . $lang . "<br>";
echo "Is RTL: " . ($isRTL ? 'Yes' : 'No') . "<br>";

echo "<h3>5. Translation Test:</h3>";
echo "t('home'): " . t('home') . "<br>";
echo "t('about'): " . t('about') . "<br>";
echo "t('contact'): " . t('contact') . "<br>";

echo "<h3>6. Database Test:</h3>";
try {
    require_once 'includes/Database.php';
    require_once 'includes/ContentManager.php';
    
    $db = new Database();
    $contentManager = new ContentManager($db);
    
    echo "Database connection: OK<br>";
    
    // Test getting destinations
    $destinations = $contentManager->getFeaturedDestinations($lang, 3);
    echo "Featured destinations count: " . count($destinations) . "<br>";
    
    if (!empty($destinations)) {
        echo "First destination name: " . $destinations[0]['name'] . "<br>";
    }
    
} catch (Exception $e) {
    echo "Database error: " . $e->getMessage() . "<br>";
}

echo "<h3>7. Test Links:</h3>";
echo '<a href="?lang=en">Switch to English</a> | ';
echo '<a href="?lang=ar">Switch to Arabic</a><br>';
echo '<a href="test-arabic.php">Go to Arabic Test Page</a><br>';
echo '<a href="index.php">Go to Homepage</a><br>';
?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Language Debug</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 20px auto; padding: 20px; }
        h2, h3 { color: #0ea5e9; }
        a { color: #0ea5e9; text-decoration: none; margin-right: 10px; }
        a:hover { text-decoration: underline; }
    </style>
</head>
<body>
