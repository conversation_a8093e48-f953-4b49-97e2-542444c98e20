<?php
session_start();

// Handle language switching
if (isset($_GET['lang']) && in_array($_GET['lang'], ['en', 'ar'])) {
    $_SESSION['lang'] = $_GET['lang'];
}

$lang = $_SESSION['lang'] ?? 'en';
$isRTL = $lang === 'ar';

echo "<!DOCTYPE html>";
echo "<html lang='{$lang}' dir='" . ($isRTL ? 'rtl' : 'ltr') . "'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>Arabic Test</title>";

// Add fonts
if ($lang === 'ar') {
    echo '<link href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">';
    echo '<style>body { font-family: "IBM Plex Sans Arabic", sans-serif; }</style>';
} else {
    echo '<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">';
    echo '<style>body { font-family: "Inter", sans-serif; }</style>';
}

echo "<script src='https://cdn.tailwindcss.com'></script>";
echo "</head>";
echo "<body class='p-8'>";

echo "<h1 class='text-3xl font-bold mb-6'>";
echo $lang === 'ar' ? 'اختبار اللغة العربية' : 'Arabic Language Test';
echo "</h1>";

echo "<div class='mb-6'>";
echo "<p><strong>Current Language:</strong> {$lang}</p>";
echo "<p><strong>Is RTL:</strong> " . ($isRTL ? 'Yes' : 'No') . "</p>";
echo "<p><strong>Session:</strong> " . ($_SESSION['lang'] ?? 'Not set') . "</p>";
echo "</div>";

echo "<div class='mb-6 space-x-4'>";
echo "<a href='?lang=en' class='bg-blue-500 text-white px-4 py-2 rounded'>English</a>";
echo "<a href='?lang=ar' class='bg-green-500 text-white px-4 py-2 rounded'>العربية</a>";
echo "</div>";

echo "<div class='grid md:grid-cols-2 gap-8'>";

echo "<div class='bg-gray-100 p-6 rounded'>";
echo "<h3 class='text-lg font-semibold mb-4'>English Text</h3>";
echo "<p>This is English text. Malaysia is a beautiful country with diverse culture.</p>";
echo "</div>";

echo "<div class='bg-gray-100 p-6 rounded'>";
echo "<h3 class='text-lg font-semibold mb-4'>Arabic Text</h3>";
echo "<p dir='rtl'>هذا نص باللغة العربية. ماليزيا بلد جميل بثقافة متنوعة.</p>";
echo "</div>";

echo "</div>";

echo "<div class='mt-8'>";
echo "<h3 class='text-lg font-semibold mb-4'>Current Content:</h3>";
if ($lang === 'ar') {
    echo "<p dir='rtl'>مرحباً بكم في موقع رحلات ماليزيا السياحية</p>";
    echo "<p dir='rtl'>نحن نقدم أفضل الرحلات السياحية إلى ماليزيا</p>";
} else {
    echo "<p>Welcome to Malaysia Tourism Trips</p>";
    echo "<p>We offer the best tourism trips to Malaysia</p>";
}
echo "</div>";

echo "<div class='mt-8'>";
echo "<a href='index.php' class='bg-primary-500 text-white px-6 py-3 rounded hover:bg-primary-600'>Go to Homepage</a>";
echo "</div>";

echo "</body>";
echo "</html>";
?>
