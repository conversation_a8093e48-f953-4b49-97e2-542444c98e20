<?php
$currentPage = basename($_SERVER['PHP_SELF'], '.php');
$lang = getCurrentLang();
$isRTL = isRTL();
?>

<header class="fixed top-0 left-0 right-0 z-50 bg-white shadow-lg transition-all duration-300" id="main-header">
    <div class="max-w-7xl mx-auto px-4">
        <div class="flex items-center justify-between h-16">
            <!-- Logo -->
            <div class="flex-shrink-0">
                <a href="index.php" class="flex items-center">
                    <img src="assets/images/logo.png" alt="<?php echo $lang === 'ar' ? 'رحلات ماليزيا السياحية' : 'Malaysia Tourism Trips'; ?>" class="h-10 w-auto">
                    <span class="ml-3 text-xl font-bold text-gray-800 hidden md:block">
                        <?php echo $lang === 'ar' ? 'رحلات ماليزيا' : 'Malaysia Tours'; ?>
                    </span>
                </a>
            </div>
            
            <!-- Desktop Navigation -->
            <nav class="hidden md:flex items-center space-x-8 <?php echo $isRTL ? 'space-x-reverse' : ''; ?>">
                <a href="index.php" class="nav-link <?php echo $currentPage === 'index' ? 'active' : ''; ?>">
                    <?php echo t('home'); ?>
                </a>
                <a href="about.php" class="nav-link <?php echo $currentPage === 'about' ? 'active' : ''; ?>">
                    <?php echo t('about'); ?>
                </a>
                <a href="gallery.php" class="nav-link <?php echo $currentPage === 'gallery' ? 'active' : ''; ?>">
                    <?php echo t('gallery'); ?>
                </a>
                <a href="blog.php" class="nav-link <?php echo $currentPage === 'blog' ? 'active' : ''; ?>">
                    <?php echo t('blog'); ?>
                </a>
                <a href="malaysia-map.php" class="nav-link <?php echo $currentPage === 'malaysia-map' ? 'active' : ''; ?>">
                    <?php echo t('malaysia_map'); ?>
                </a>
                <a href="contact.php" class="nav-link <?php echo $currentPage === 'contact' ? 'active' : ''; ?>">
                    <?php echo t('contact'); ?>
                </a>
            </nav>
            
            <!-- Language Switcher & Mobile Menu Button -->
            <div class="flex items-center space-x-4 <?php echo $isRTL ? 'space-x-reverse' : ''; ?>">
                <!-- Language Switcher -->
                <div class="relative">
                    <button id="language-toggle" class="flex items-center space-x-2 text-gray-600 hover:text-gray-800 transition-colors <?php echo $isRTL ? 'space-x-reverse' : ''; ?>">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"></path>
                        </svg>
                        <span class="hidden sm:inline"><?php echo $lang === 'ar' ? 'العربية' : 'English'; ?></span>
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    
                    <div id="language-dropdown" class="absolute <?php echo $isRTL ? 'left-0' : 'right-0'; ?> mt-2 w-32 bg-white rounded-md shadow-lg py-1 z-50 hidden">
                        <a href="?lang=en" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 <?php echo $lang === 'en' ? 'bg-gray-50 font-semibold' : ''; ?>">
                            English
                        </a>
                        <a href="?lang=ar" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 <?php echo $lang === 'ar' ? 'bg-gray-50 font-semibold' : ''; ?>">
                            العربية
                        </a>
                    </div>
                </div>
                
                <!-- WhatsApp Button -->
                <a href="https://wa.me/<?php echo WHATSAPP_NUMBER; ?>" target="_blank" class="hidden sm:flex items-center bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg text-sm font-semibold transition-colors">
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
                    </svg>
                    <?php echo t('whatsapp'); ?>
                </a>
                
                <!-- Mobile Menu Button -->
                <button id="mobile-menu-button" class="md:hidden flex items-center justify-center w-10 h-10 rounded-lg text-gray-600 hover:text-gray-800 hover:bg-gray-100 transition-colors">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
            </div>
        </div>
    </div>
    
    <!-- Mobile Navigation -->
    <div id="mobile-menu" class="md:hidden bg-white border-t border-gray-200 hidden">
        <div class="px-4 py-2 space-y-1">
            <a href="index.php" class="mobile-nav-link <?php echo $currentPage === 'index' ? 'active' : ''; ?>">
                <?php echo t('home'); ?>
            </a>
            <a href="about.php" class="mobile-nav-link <?php echo $currentPage === 'about' ? 'active' : ''; ?>">
                <?php echo t('about'); ?>
            </a>
            <a href="gallery.php" class="mobile-nav-link <?php echo $currentPage === 'gallery' ? 'active' : ''; ?>">
                <?php echo t('gallery'); ?>
            </a>
            <a href="blog.php" class="mobile-nav-link <?php echo $currentPage === 'blog' ? 'active' : ''; ?>">
                <?php echo t('blog'); ?>
            </a>
            <a href="malaysia-map.php" class="mobile-nav-link <?php echo $currentPage === 'malaysia-map' ? 'active' : ''; ?>">
                <?php echo t('malaysia_map'); ?>
            </a>
            <a href="contact.php" class="mobile-nav-link <?php echo $currentPage === 'contact' ? 'active' : ''; ?>">
                <?php echo t('contact'); ?>
            </a>
            
            <!-- Mobile WhatsApp Button -->
            <div class="pt-4 border-t border-gray-200">
                <a href="https://wa.me/<?php echo WHATSAPP_NUMBER; ?>" target="_blank" class="flex items-center justify-center bg-green-500 hover:bg-green-600 text-white px-4 py-3 rounded-lg font-semibold transition-colors">
                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
                    </svg>
                    <?php echo t('whatsapp'); ?>
                </a>
            </div>
        </div>
    </div>
</header>

<!-- Floating WhatsApp Button -->
<div class="fixed bottom-6 <?php echo $isRTL ? 'left-6' : 'right-6'; ?> z-40">
    <a href="https://wa.me/<?php echo WHATSAPP_NUMBER; ?>" target="_blank" class="flex items-center justify-center w-14 h-14 bg-green-500 hover:bg-green-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110 animate-pulse">
        <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
            <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
        </svg>
    </a>
</div>

<script>
// Header scroll effect
window.addEventListener('scroll', function() {
    const header = document.getElementById('main-header');
    if (window.scrollY > 100) {
        header.classList.add('bg-white/95', 'backdrop-blur-sm');
    } else {
        header.classList.remove('bg-white/95', 'backdrop-blur-sm');
    }
});

// Mobile menu toggle
document.getElementById('mobile-menu-button').addEventListener('click', function() {
    const mobileMenu = document.getElementById('mobile-menu');
    mobileMenu.classList.toggle('hidden');
});

// Language dropdown toggle
document.getElementById('language-toggle').addEventListener('click', function() {
    const dropdown = document.getElementById('language-dropdown');
    dropdown.classList.toggle('hidden');
});

// Close dropdowns when clicking outside
document.addEventListener('click', function(event) {
    const languageToggle = document.getElementById('language-toggle');
    const languageDropdown = document.getElementById('language-dropdown');
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const mobileMenu = document.getElementById('mobile-menu');
    
    if (!languageToggle.contains(event.target)) {
        languageDropdown.classList.add('hidden');
    }
    
    if (!mobileMenuButton.contains(event.target) && !mobileMenu.contains(event.target)) {
        mobileMenu.classList.add('hidden');
    }
});
</script>

<style>
.nav-link {
    @apply text-gray-600 hover:text-primary-500 font-medium transition-colors duration-200 relative;
}

.nav-link.active {
    @apply text-primary-500;
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    right: 0;
    height: 2px;
    background-color: theme('colors.primary.500');
}

.mobile-nav-link {
    @apply block px-3 py-2 text-gray-600 hover:text-primary-500 hover:bg-gray-50 rounded-md font-medium transition-colors duration-200;
}

.mobile-nav-link.active {
    @apply text-primary-500 bg-primary-50;
}
</style>
