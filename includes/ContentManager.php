<?php
class ContentManager {
    private $db;
    
    public function __construct(Database $database) {
        $this->db = $database;
    }
    
    // Articles methods
    public function getArticles($lang = 'en', $limit = null, $offset = 0, $featured = null) {
        $where = "is_published = 1";
        $params = [];
        
        if ($featured !== null) {
            $where .= " AND is_featured = ?";
            $params[] = $featured;
        }
        
        $orderBy = "ORDER BY created_at DESC";
        $limitClause = $limit ? "LIMIT ? OFFSET ?" : "";
        
        if ($limit) {
            $params[] = $limit;
            $params[] = $offset;
        }
        
        $sql = "SELECT id, 
                       title_{$lang} as title,
                       slug_{$lang} as slug,
                       excerpt_{$lang} as excerpt,
                       content_{$lang} as content,
                       featured_image,
                       category_{$lang} as category,
                       tags_{$lang} as tags,
                       is_featured,
                       views,
                       created_at,
                       updated_at
                FROM articles 
                WHERE {$where} 
                {$orderBy} 
                {$limitClause}";
        
        return $this->db->fetchAll($sql, $params);
    }
    
    public function getArticleById($id, $lang = 'en') {
        $sql = "SELECT id, 
                       title_{$lang} as title,
                       slug_{$lang} as slug,
                       excerpt_{$lang} as excerpt,
                       content_{$lang} as content,
                       featured_image,
                       category_{$lang} as category,
                       tags_{$lang} as tags,
                       meta_title_{$lang} as meta_title,
                       meta_description_{$lang} as meta_description,
                       is_featured,
                       views,
                       created_at,
                       updated_at
                FROM articles 
                WHERE id = ? AND is_published = 1";
        
        return $this->db->fetch($sql, [$id]);
    }
    
    public function getArticleBySlug($slug, $lang = 'en') {
        $sql = "SELECT id, 
                       title_{$lang} as title,
                       slug_{$lang} as slug,
                       excerpt_{$lang} as excerpt,
                       content_{$lang} as content,
                       featured_image,
                       category_{$lang} as category,
                       tags_{$lang} as tags,
                       meta_title_{$lang} as meta_title,
                       meta_description_{$lang} as meta_description,
                       is_featured,
                       views,
                       created_at,
                       updated_at
                FROM articles 
                WHERE slug_{$lang} = ? AND is_published = 1";
        
        return $this->db->fetch($sql, [$slug]);
    }
    
    public function getRecentArticles($lang = 'en', $limit = 5) {
        return $this->getArticles($lang, $limit);
    }
    
    public function getFeaturedArticles($lang = 'en', $limit = 3) {
        return $this->getArticles($lang, $limit, 0, true);
    }
    
    public function incrementArticleViews($id) {
        $sql = "UPDATE articles SET views = views + 1 WHERE id = ?";
        return $this->db->query($sql, [$id]);
    }
    
    public function searchArticles($query, $lang = 'en', $limit = 20, $offset = 0) {
        $sql = "SELECT id, 
                       title_{$lang} as title,
                       slug_{$lang} as slug,
                       excerpt_{$lang} as excerpt,
                       featured_image,
                       category_{$lang} as category,
                       created_at
                FROM articles 
                WHERE is_published = 1 
                AND (title_{$lang} LIKE ? OR excerpt_{$lang} LIKE ? OR content_{$lang} LIKE ?)
                ORDER BY created_at DESC 
                LIMIT ? OFFSET ?";
        
        $searchTerm = "%{$query}%";
        return $this->db->fetchAll($sql, [$searchTerm, $searchTerm, $searchTerm, $limit, $offset]);
    }
    
    // Destinations methods
    public function getDestinations($lang = 'en', $limit = null, $featured = null) {
        $where = "is_active = 1";
        $params = [];
        
        if ($featured !== null) {
            $where .= " AND is_featured = ?";
            $params[] = $featured;
        }
        
        $orderBy = "ORDER BY sort_order ASC, created_at DESC";
        $limitClause = $limit ? "LIMIT ?" : "";
        
        if ($limit) {
            $params[] = $limit;
        }
        
        $sql = "SELECT id, 
                       name_{$lang} as name,
                       slug_{$lang} as slug,
                       description_{$lang} as description,
                       image,
                       price,
                       duration_{$lang} as duration,
                       location_{$lang} as location,
                       highlights_{$lang} as highlights,
                       is_featured
                FROM destinations 
                WHERE {$where} 
                {$orderBy} 
                {$limitClause}";
        
        return $this->db->fetchAll($sql, $params);
    }
    
    public function getFeaturedDestinations($lang = 'en', $limit = 6) {
        return $this->getDestinations($lang, $limit, true);
    }
    
    public function getDestinationById($id, $lang = 'en') {
        $sql = "SELECT id, 
                       name_{$lang} as name,
                       slug_{$lang} as slug,
                       description_{$lang} as description,
                       image,
                       price,
                       duration_{$lang} as duration,
                       location_{$lang} as location,
                       highlights_{$lang} as highlights,
                       is_featured
                FROM destinations 
                WHERE id = ? AND is_active = 1";
        
        return $this->db->fetch($sql, [$id]);
    }
    
    // Gallery methods
    public function getGalleryImages($limit = null, $category = null, $featured = null) {
        $where = "1=1";
        $params = [];
        
        if ($category) {
            $lang = getCurrentLang();
            $where .= " AND category_{$lang} = ?";
            $params[] = $category;
        }
        
        if ($featured !== null) {
            $where .= " AND is_featured = ?";
            $params[] = $featured;
        }
        
        $orderBy = "ORDER BY sort_order ASC, created_at DESC";
        $limitClause = $limit ? "LIMIT ?" : "";
        
        if ($limit) {
            $params[] = $limit;
        }
        
        $lang = getCurrentLang();
        $sql = "SELECT id, 
                       title_{$lang} as title,
                       description_{$lang} as description,
                       image as url,
                       thumbnail,
                       category_{$lang} as category,
                       is_featured
                FROM gallery 
                WHERE {$where} 
                {$orderBy} 
                {$limitClause}";
        
        return $this->db->fetchAll($sql, $params);
    }
    
    public function getGalleryCategories($lang = 'en') {
        $sql = "SELECT DISTINCT category_{$lang} as category 
                FROM gallery 
                WHERE category_{$lang} IS NOT NULL AND category_{$lang} != ''
                ORDER BY category_{$lang}";
        
        return $this->db->fetchAll($sql);
    }
    
    // Testimonials methods
    public function getTestimonials($lang = 'en', $limit = null, $featured = null) {
        $where = "is_active = 1";
        $params = [];
        
        if ($featured !== null) {
            $where .= " AND is_featured = ?";
            $params[] = $featured;
        }
        
        $orderBy = "ORDER BY sort_order ASC, created_at DESC";
        $limitClause = $limit ? "LIMIT ?" : "";
        
        if ($limit) {
            $params[] = $limit;
        }
        
        $sql = "SELECT id, 
                       name,
                       review_{$lang} as review,
                       rating,
                       avatar,
                       location_{$lang} as location,
                       is_featured
                FROM testimonials 
                WHERE {$where} 
                {$orderBy} 
                {$limitClause}";
        
        return $this->db->fetchAll($sql, $params);
    }
    
    // Malaysia states methods
    public function getMalaysiaStates($lang = 'en') {
        $sql = "SELECT id, 
                       name_{$lang} as name,
                       code,
                       description_{$lang} as description,
                       highlights_{$lang} as highlights,
                       image,
                       coordinates
                FROM malaysia_states 
                WHERE is_active = 1 
                ORDER BY sort_order ASC, name_{$lang} ASC";
        
        return $this->db->fetchAll($sql);
    }
    
    public function getStateByCode($code, $lang = 'en') {
        $sql = "SELECT id, 
                       name_{$lang} as name,
                       code,
                       description_{$lang} as description,
                       highlights_{$lang} as highlights,
                       image,
                       coordinates
                FROM malaysia_states 
                WHERE code = ? AND is_active = 1";
        
        return $this->db->fetch($sql, [$code]);
    }
    
    // Contact methods
    public function saveContactMessage($data) {
        return $this->db->insert('contact_messages', $data);
    }
    
    public function subscribeNewsletter($email) {
        // Check if email already exists
        if ($this->db->exists('newsletter_subscribers', 'email = ?', [$email])) {
            // Reactivate if previously unsubscribed
            return $this->db->update(
                'newsletter_subscribers',
                ['is_active' => 1, 'unsubscribed_at' => null],
                'email = ?',
                [$email]
            );
        } else {
            // Insert new subscriber
            return $this->db->insert('newsletter_subscribers', [
                'email' => $email,
                'is_active' => 1
            ]);
        }
    }
    
    // Site settings methods
    public function getSetting($key, $lang = 'en') {
        $sql = "SELECT setting_value_{$lang} as value 
                FROM site_settings 
                WHERE setting_key = ? AND is_active = 1";
        
        $result = $this->db->fetch($sql, [$key]);
        return $result ? $result['value'] : null;
    }
    
    public function getSettings($lang = 'en') {
        $sql = "SELECT setting_key, setting_value_{$lang} as value 
                FROM site_settings 
                WHERE is_active = 1";
        
        $results = $this->db->fetchAll($sql);
        $settings = [];
        
        foreach ($results as $row) {
            $settings[$row['setting_key']] = $row['value'];
        }
        
        return $settings;
    }
    
    // Statistics methods
    public function getStats() {
        $stats = [];
        
        $stats['total_articles'] = $this->db->count('articles', 'is_published = 1');
        $stats['total_destinations'] = $this->db->count('destinations', 'is_active = 1');
        $stats['total_gallery_images'] = $this->db->count('gallery');
        $stats['total_testimonials'] = $this->db->count('testimonials', 'is_active = 1');
        $stats['total_contact_messages'] = $this->db->count('contact_messages');
        $stats['total_newsletter_subscribers'] = $this->db->count('newsletter_subscribers', 'is_active = 1');
        
        return $stats;
    }

    public function getGalleryCategories($lang = 'en') {
        // Return static categories for now
        $categories = [
            ['category' => $lang === 'ar' ? 'طبيعة' : 'Nature'],
            ['category' => $lang === 'ar' ? 'مدن' : 'Cities'],
            ['category' => $lang === 'ar' ? 'ثقافة' : 'Culture'],
            ['category' => $lang === 'ar' ? 'طعام' : 'Food'],
            ['category' => $lang === 'ar' ? 'مغامرات' : 'Adventure']
        ];

        return $categories;
    }

    public function getGalleryImages() {
        // Return static gallery data for now
        $images = [
            [
                'id' => 1,
                'url' => 'assets/images/gallery/petronas-towers.jpg',
                'thumbnail' => 'assets/images/gallery/thumbs/petronas-towers.jpg',
                'title' => 'Petronas Twin Towers',
                'description' => 'Iconic twin towers in Kuala Lumpur',
                'category' => 'Cities'
            ],
            [
                'id' => 2,
                'url' => 'assets/images/gallery/penang-street-art.jpg',
                'thumbnail' => 'assets/images/gallery/thumbs/penang-street-art.jpg',
                'title' => 'Penang Street Art',
                'description' => 'Famous street art in George Town',
                'category' => 'Culture'
            ],
            [
                'id' => 3,
                'url' => 'assets/images/gallery/mount-kinabalu.jpg',
                'thumbnail' => 'assets/images/gallery/thumbs/mount-kinabalu.jpg',
                'title' => 'Mount Kinabalu',
                'description' => 'Highest mountain in Southeast Asia',
                'category' => 'Nature'
            ]
        ];

        return $images;
    }
}
?>
