<?php
// Database configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'travel_website');
define('DB_USER', 'root');
define('DB_PASS', '');

// Site configuration
define('SITE_URL', 'http://localhost/travel');
define('SITE_NAME_EN', 'Malaysia Tourism Trips');
define('SITE_NAME_AR', 'رحلات ماليزيا السياحية');

// Upload directories
define('UPLOAD_DIR', 'uploads/');
define('GALLERY_DIR', UPLOAD_DIR . 'gallery/');
define('ARTICLES_DIR', UPLOAD_DIR . 'articles/');
define('TESTIMONIALS_DIR', UPLOAD_DIR . 'testimonials/');

// Pagination
define('ARTICLES_PER_PAGE', 9);
define('GALLERY_PER_PAGE', 12);

// Contact settings
define('CONTACT_EMAIL', '<EMAIL>');
define('WHATSAPP_NUMBER', '60123456789');

// Language settings
define('DEFAULT_LANG', 'en');
define('SUPPORTED_LANGS', ['en', 'ar']);

// Admin settings
define('ADMIN_SESSION_TIMEOUT', 3600); // 1 hour

// Error reporting (set to 0 in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Timezone
date_default_timezone_set('Asia/Kuala_Lumpur');

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Helper functions
function getCurrentLang() {
    return $_SESSION['lang'] ?? DEFAULT_LANG;
}

function isRTL() {
    return getCurrentLang() === 'ar';
}

function sanitizeInput($input) {
    return htmlspecialchars(strip_tags(trim($input)), ENT_QUOTES, 'UTF-8');
}

function formatDate($date, $lang = null) {
    if (!$lang) $lang = getCurrentLang();
    
    if ($lang === 'ar') {
        return date('d/m/Y', strtotime($date));
    } else {
        return date('M d, Y', strtotime($date));
    }
}

function generateSlug($text) {
    // Convert to lowercase
    $text = strtolower($text);
    
    // Replace spaces and special characters with hyphens
    $text = preg_replace('/[^a-z0-9\-]/', '-', $text);
    
    // Remove multiple consecutive hyphens
    $text = preg_replace('/-+/', '-', $text);
    
    // Remove leading and trailing hyphens
    $text = trim($text, '-');
    
    return $text;
}

function uploadFile($file, $directory, $allowedTypes = ['jpg', 'jpeg', 'png', 'gif']) {
    if (!isset($file['tmp_name']) || empty($file['tmp_name'])) {
        return ['success' => false, 'message' => 'No file uploaded'];
    }
    
    $uploadDir = $directory;
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }
    
    $fileName = $file['name'];
    $fileSize = $file['size'];
    $fileTmp = $file['tmp_name'];
    $fileType = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
    
    // Check file type
    if (!in_array($fileType, $allowedTypes)) {
        return ['success' => false, 'message' => 'Invalid file type'];
    }
    
    // Check file size (5MB max)
    if ($fileSize > 5 * 1024 * 1024) {
        return ['success' => false, 'message' => 'File too large'];
    }
    
    // Generate unique filename
    $newFileName = uniqid() . '.' . $fileType;
    $uploadPath = $uploadDir . $newFileName;
    
    if (move_uploaded_file($fileTmp, $uploadPath)) {
        return ['success' => true, 'filename' => $newFileName, 'path' => $uploadPath];
    } else {
        return ['success' => false, 'message' => 'Upload failed'];
    }
}

function resizeImage($source, $destination, $maxWidth, $maxHeight, $quality = 85) {
    $imageInfo = getimagesize($source);
    if (!$imageInfo) return false;
    
    $width = $imageInfo[0];
    $height = $imageInfo[1];
    $type = $imageInfo[2];
    
    // Calculate new dimensions
    $ratio = min($maxWidth / $width, $maxHeight / $height);
    $newWidth = intval($width * $ratio);
    $newHeight = intval($height * $ratio);
    
    // Create image resource
    switch ($type) {
        case IMAGETYPE_JPEG:
            $sourceImage = imagecreatefromjpeg($source);
            break;
        case IMAGETYPE_PNG:
            $sourceImage = imagecreatefrompng($source);
            break;
        case IMAGETYPE_GIF:
            $sourceImage = imagecreatefromgif($source);
            break;
        default:
            return false;
    }
    
    // Create new image
    $newImage = imagecreatetruecolor($newWidth, $newHeight);
    
    // Preserve transparency for PNG and GIF
    if ($type == IMAGETYPE_PNG || $type == IMAGETYPE_GIF) {
        imagealphablending($newImage, false);
        imagesavealpha($newImage, true);
        $transparent = imagecolorallocatealpha($newImage, 255, 255, 255, 127);
        imagefilledrectangle($newImage, 0, 0, $newWidth, $newHeight, $transparent);
    }
    
    // Resize image
    imagecopyresampled($newImage, $sourceImage, 0, 0, 0, 0, $newWidth, $newHeight, $width, $height);
    
    // Save image
    switch ($type) {
        case IMAGETYPE_JPEG:
            imagejpeg($newImage, $destination, $quality);
            break;
        case IMAGETYPE_PNG:
            imagepng($newImage, $destination);
            break;
        case IMAGETYPE_GIF:
            imagegif($newImage, $destination);
            break;
    }
    
    // Clean up
    imagedestroy($sourceImage);
    imagedestroy($newImage);
    
    return true;
}

function createThumbnail($source, $destination, $size = 300) {
    return resizeImage($source, $destination, $size, $size);
}

// Language translations
function t($key, $lang = null) {
    if (!$lang) $lang = getCurrentLang();
    
    $translations = [
        'en' => [
            'home' => 'Home',
            'about' => 'About Us',
            'contact' => 'Contact',
            'gallery' => 'Gallery',
            'blog' => 'Blog',
            'malaysia_map' => 'Malaysia Map',
            'language' => 'Language',
            'search' => 'Search',
            'read_more' => 'Read More',
            'view_more' => 'View More',
            'contact_us' => 'Contact Us',
            'whatsapp' => 'WhatsApp',
            'email' => 'Email',
            'phone' => 'Phone',
            'address' => 'Address',
            'send_message' => 'Send Message',
            'name' => 'Name',
            'message' => 'Message',
            'subscribe' => 'Subscribe',
            'newsletter' => 'Newsletter',
            'all_rights_reserved' => 'All rights reserved',
            'privacy_policy' => 'Privacy Policy',
            'terms_conditions' => 'Terms & Conditions'
        ],
        'ar' => [
            'home' => 'الرئيسية',
            'about' => 'من نحن',
            'contact' => 'اتصل بنا',
            'gallery' => 'معرض الصور',
            'blog' => 'المدونة',
            'malaysia_map' => 'خريطة ماليزيا',
            'language' => 'اللغة',
            'search' => 'بحث',
            'read_more' => 'اقرأ المزيد',
            'view_more' => 'عرض المزيد',
            'contact_us' => 'تواصل معنا',
            'whatsapp' => 'واتساب',
            'email' => 'البريد الإلكتروني',
            'phone' => 'الهاتف',
            'address' => 'العنوان',
            'send_message' => 'إرسال رسالة',
            'name' => 'الاسم',
            'message' => 'الرسالة',
            'subscribe' => 'اشترك',
            'newsletter' => 'النشرة الإخبارية',
            'all_rights_reserved' => 'جميع الحقوق محفوظة',
            'privacy_policy' => 'سياسة الخصوصية',
            'terms_conditions' => 'الشروط والأحكام'
        ]
    ];
    
    return $translations[$lang][$key] ?? $key;
}
?>
