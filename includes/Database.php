<?php
class Database {
    private $host;
    private $dbname;
    private $username;
    private $password;
    private $pdo;
    
    public function __construct() {
        $this->host = DB_HOST;
        $this->dbname = DB_NAME;
        $this->username = DB_USER;
        $this->password = DB_PASS;
        
        $this->connect();
    }
    
    private function connect() {
        try {
            $dsn = "mysql:host={$this->host};dbname={$this->dbname};charset=utf8mb4";
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
            ];
            
            $this->pdo = new PDO($dsn, $this->username, $this->password, $options);
        } catch (PDOException $e) {
            die("Database connection failed: " . $e->getMessage());
        }
    }
    
    public function getConnection() {
        return $this->pdo;
    }
    
    public function query($sql, $params = []) {
        try {
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            error_log("Database query error: " . $e->getMessage());
            return false;
        }
    }
    
    public function fetch($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt ? $stmt->fetch() : false;
    }
    
    public function fetchAll($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt ? $stmt->fetchAll() : [];
    }
    
    public function insert($table, $data) {
        $columns = implode(',', array_keys($data));
        $placeholders = ':' . implode(', :', array_keys($data));
        
        $sql = "INSERT INTO {$table} ({$columns}) VALUES ({$placeholders})";
        $stmt = $this->query($sql, $data);
        
        return $stmt ? $this->pdo->lastInsertId() : false;
    }
    
    public function update($table, $data, $where, $whereParams = []) {
        $setClause = [];
        foreach (array_keys($data) as $key) {
            $setClause[] = "{$key} = :{$key}";
        }
        $setClause = implode(', ', $setClause);
        
        $sql = "UPDATE {$table} SET {$setClause} WHERE {$where}";
        $params = array_merge($data, $whereParams);
        
        return $this->query($sql, $params);
    }
    
    public function delete($table, $where, $params = []) {
        $sql = "DELETE FROM {$table} WHERE {$where}";
        return $this->query($sql, $params);
    }
    
    public function count($table, $where = '1=1', $params = []) {
        $sql = "SELECT COUNT(*) as count FROM {$table} WHERE {$where}";
        $result = $this->fetch($sql, $params);
        return $result ? $result['count'] : 0;
    }
    
    public function exists($table, $where, $params = []) {
        return $this->count($table, $where, $params) > 0;
    }
    
    public function beginTransaction() {
        return $this->pdo->beginTransaction();
    }
    
    public function commit() {
        return $this->pdo->commit();
    }
    
    public function rollback() {
        return $this->pdo->rollback();
    }
    
    public function createTables() {
        $tables = [
            // Admin users table
            "CREATE TABLE IF NOT EXISTS admin_users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) UNIQUE NOT NULL,
                email VARCHAR(100) UNIQUE NOT NULL,
                password VARCHAR(255) NOT NULL,
                full_name VARCHAR(100) NOT NULL,
                role ENUM('admin', 'editor') DEFAULT 'editor',
                is_active BOOLEAN DEFAULT TRUE,
                last_login TIMESTAMP NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )",
            
            // Articles table
            "CREATE TABLE IF NOT EXISTS articles (
                id INT AUTO_INCREMENT PRIMARY KEY,
                title_en VARCHAR(255) NOT NULL,
                title_ar VARCHAR(255) NOT NULL,
                slug_en VARCHAR(255) UNIQUE NOT NULL,
                slug_ar VARCHAR(255) UNIQUE NOT NULL,
                excerpt_en TEXT,
                excerpt_ar TEXT,
                content_en LONGTEXT NOT NULL,
                content_ar LONGTEXT NOT NULL,
                featured_image VARCHAR(255),
                category_en VARCHAR(100),
                category_ar VARCHAR(100),
                tags_en TEXT,
                tags_ar TEXT,
                meta_title_en VARCHAR(255),
                meta_title_ar VARCHAR(255),
                meta_description_en TEXT,
                meta_description_ar TEXT,
                is_featured BOOLEAN DEFAULT FALSE,
                is_published BOOLEAN DEFAULT TRUE,
                views INT DEFAULT 0,
                author_id INT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (author_id) REFERENCES admin_users(id) ON DELETE SET NULL,
                INDEX idx_published (is_published),
                INDEX idx_featured (is_featured),
                INDEX idx_created (created_at)
            )",
            
            // Destinations table
            "CREATE TABLE IF NOT EXISTS destinations (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name_en VARCHAR(255) NOT NULL,
                name_ar VARCHAR(255) NOT NULL,
                slug_en VARCHAR(255) UNIQUE NOT NULL,
                slug_ar VARCHAR(255) UNIQUE NOT NULL,
                description_en TEXT,
                description_ar TEXT,
                image VARCHAR(255),
                price VARCHAR(100),
                duration_en VARCHAR(100),
                duration_ar VARCHAR(100),
                location_en VARCHAR(255),
                location_ar VARCHAR(255),
                highlights_en TEXT,
                highlights_ar TEXT,
                is_featured BOOLEAN DEFAULT FALSE,
                is_active BOOLEAN DEFAULT TRUE,
                sort_order INT DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_featured (is_featured),
                INDEX idx_active (is_active),
                INDEX idx_sort (sort_order)
            )",
            
            // Gallery table
            "CREATE TABLE IF NOT EXISTS gallery (
                id INT AUTO_INCREMENT PRIMARY KEY,
                title_en VARCHAR(255),
                title_ar VARCHAR(255),
                description_en TEXT,
                description_ar TEXT,
                image VARCHAR(255) NOT NULL,
                thumbnail VARCHAR(255),
                category_en VARCHAR(100),
                category_ar VARCHAR(100),
                destination_id INT NULL,
                is_featured BOOLEAN DEFAULT FALSE,
                sort_order INT DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (destination_id) REFERENCES destinations(id) ON DELETE SET NULL,
                INDEX idx_featured (is_featured),
                INDEX idx_category_en (category_en),
                INDEX idx_category_ar (category_ar),
                INDEX idx_sort (sort_order)
            )",
            
            // Testimonials table
            "CREATE TABLE IF NOT EXISTS testimonials (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                review_en TEXT NOT NULL,
                review_ar TEXT NOT NULL,
                rating INT DEFAULT 5,
                avatar VARCHAR(255),
                location_en VARCHAR(100),
                location_ar VARCHAR(100),
                is_featured BOOLEAN DEFAULT FALSE,
                is_active BOOLEAN DEFAULT TRUE,
                sort_order INT DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_featured (is_featured),
                INDEX idx_active (is_active),
                INDEX idx_rating (rating),
                INDEX idx_sort (sort_order)
            )",
            
            // Contact messages table
            "CREATE TABLE IF NOT EXISTS contact_messages (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                email VARCHAR(100) NOT NULL,
                phone VARCHAR(20),
                subject VARCHAR(255),
                message TEXT NOT NULL,
                is_read BOOLEAN DEFAULT FALSE,
                is_replied BOOLEAN DEFAULT FALSE,
                ip_address VARCHAR(45),
                user_agent TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_read (is_read),
                INDEX idx_replied (is_replied),
                INDEX idx_created (created_at)
            )",
            
            // Newsletter subscribers table
            "CREATE TABLE IF NOT EXISTS newsletter_subscribers (
                id INT AUTO_INCREMENT PRIMARY KEY,
                email VARCHAR(100) UNIQUE NOT NULL,
                is_active BOOLEAN DEFAULT TRUE,
                subscribed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                unsubscribed_at TIMESTAMP NULL,
                INDEX idx_active (is_active),
                INDEX idx_email (email)
            )",
            
            // Malaysia states table
            "CREATE TABLE IF NOT EXISTS malaysia_states (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name_en VARCHAR(100) NOT NULL,
                name_ar VARCHAR(100) NOT NULL,
                code VARCHAR(10) UNIQUE NOT NULL,
                description_en TEXT,
                description_ar TEXT,
                highlights_en TEXT,
                highlights_ar TEXT,
                image VARCHAR(255),
                coordinates TEXT,
                is_active BOOLEAN DEFAULT TRUE,
                sort_order INT DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_code (code),
                INDEX idx_active (is_active),
                INDEX idx_sort (sort_order)
            )",
            
            // Site settings table
            "CREATE TABLE IF NOT EXISTS site_settings (
                id INT AUTO_INCREMENT PRIMARY KEY,
                setting_key VARCHAR(100) UNIQUE NOT NULL,
                setting_value_en TEXT,
                setting_value_ar TEXT,
                setting_type ENUM('text', 'textarea', 'image', 'boolean', 'number') DEFAULT 'text',
                is_active BOOLEAN DEFAULT TRUE,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_key (setting_key),
                INDEX idx_active (is_active)
            )"
        ];
        
        foreach ($tables as $sql) {
            $this->query($sql);
        }
        
        // Insert default admin user if not exists
        if (!$this->exists('admin_users', 'username = ?', ['admin'])) {
            $this->insert('admin_users', [
                'username' => 'admin',
                'email' => '<EMAIL>',
                'password' => password_hash('admin123', PASSWORD_DEFAULT),
                'full_name' => 'System Administrator',
                'role' => 'admin'
            ]);
        }
        
        // Insert default site settings
        $defaultSettings = [
            ['site_title', 'Malaysia Tourism Trips', 'رحلات ماليزيا السياحية'],
            ['site_description', 'Exceptional tourism trips to Malaysia and beautiful destinations', 'رحلات سياحية استثنائية إلى ماليزيا وأجمل الوجهات'],
            ['contact_email', '<EMAIL>', '<EMAIL>'],
            ['contact_phone', '+60 12 345 6789', '+60 12 345 6789'],
            ['contact_whatsapp', '60123456789', '60123456789'],
            ['contact_address', 'Kuala Lumpur, Malaysia', 'كوالالمبور، ماليزيا'],
            ['facebook_url', 'https://facebook.com/malaysiaturism', 'https://facebook.com/malaysiaturism'],
            ['instagram_url', 'https://instagram.com/malaysiaturism', 'https://instagram.com/malaysiaturism'],
            ['twitter_url', 'https://twitter.com/malaysiaturism', 'https://twitter.com/malaysiaturism']
        ];
        
        foreach ($defaultSettings as $setting) {
            if (!$this->exists('site_settings', 'setting_key = ?', [$setting[0]])) {
                $this->insert('site_settings', [
                    'setting_key' => $setting[0],
                    'setting_value_en' => $setting[1],
                    'setting_value_ar' => $setting[2]
                ]);
            }
        }
        
        return true;
    }
}
?>
