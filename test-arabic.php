<?php
session_start();
require_once 'includes/config.php';

// Handle language switching
if (isset($_GET['lang']) && in_array($_GET['lang'], SUPPORTED_LANGS)) {
    $_SESSION['lang'] = $_GET['lang'];
}

$lang = $_SESSION['lang'] ?? DEFAULT_LANG;
$isRTL = $lang === 'ar';

echo "Current language: " . $lang . "<br>";
echo "Is RTL: " . ($isRTL ? 'Yes' : 'No') . "<br>";
echo "Session lang: " . ($_SESSION['lang'] ?? 'Not set') . "<br>";
?>

<!DOCTYPE html>
<html lang="<?php echo $lang; ?>" dir="<?php echo $isRTL ? 'rtl' : 'ltr'; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Arabic Language Test</title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <?php if ($lang === 'ar'): ?>
        <link href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <?php else: ?>
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <?php endif; ?>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'arabic': ['IBM Plex Sans Arabic', 'sans-serif'],
                        'english': ['Inter', 'sans-serif']
                    }
                }
            }
        }
    </script>
    
    <style>
        .font-arabic {
            font-family: 'IBM Plex Sans Arabic', sans-serif;
        }
        .font-english {
            font-family: 'Inter', sans-serif;
        }
    </style>
</head>
<body class="<?php echo $lang === 'ar' ? 'font-arabic' : 'font-english'; ?> p-8">
    
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-6">
            <?php echo $lang === 'ar' ? 'اختبار اللغة العربية' : 'Arabic Language Test'; ?>
        </h1>
        
        <div class="mb-6">
            <h2 class="text-xl font-semibold mb-4">Language Switcher:</h2>
            <div class="space-x-4 <?php echo $isRTL ? 'space-x-reverse' : ''; ?>">
                <a href="?lang=en" class="bg-blue-500 text-white px-4 py-2 rounded <?php echo $lang === 'en' ? 'bg-blue-700' : ''; ?>">
                    English
                </a>
                <a href="?lang=ar" class="bg-green-500 text-white px-4 py-2 rounded <?php echo $lang === 'ar' ? 'bg-green-700' : ''; ?>">
                    العربية
                </a>
            </div>
        </div>
        
        <div class="grid md:grid-cols-2 gap-8">
            <div class="bg-gray-100 p-6 rounded-lg">
                <h3 class="text-lg font-semibold mb-4">English Content</h3>
                <p class="font-english">
                    This is English text using Inter font. 
                    Malaysia is a beautiful country with diverse culture and amazing destinations.
                </p>
            </div>
            
            <div class="bg-gray-100 p-6 rounded-lg">
                <h3 class="text-lg font-semibold mb-4">Arabic Content</h3>
                <p class="font-arabic" dir="rtl">
                    هذا نص باللغة العربية باستخدام خط IBM Plex Sans Arabic.
                    ماليزيا بلد جميل بثقافة متنوعة ووجهات مذهلة.
                </p>
            </div>
        </div>
        
        <div class="mt-8">
            <h3 class="text-lg font-semibold mb-4">Translation Function Test:</h3>
            <ul class="space-y-2">
                <li><strong>Home:</strong> <?php echo t('home'); ?></li>
                <li><strong>About:</strong> <?php echo t('about'); ?></li>
                <li><strong>Contact:</strong> <?php echo t('contact'); ?></li>
                <li><strong>Gallery:</strong> <?php echo t('gallery'); ?></li>
                <li><strong>Blog:</strong> <?php echo t('blog'); ?></li>
            </ul>
        </div>
        
        <div class="mt-8">
            <h3 class="text-lg font-semibold mb-4">RTL Layout Test:</h3>
            <div class="flex items-center space-x-4 <?php echo $isRTL ? 'space-x-reverse' : ''; ?> bg-blue-50 p-4 rounded">
                <div class="w-12 h-12 bg-blue-500 rounded"></div>
                <div>
                    <h4 class="font-semibold">
                        <?php echo $lang === 'ar' ? 'عنوان تجريبي' : 'Test Title'; ?>
                    </h4>
                    <p class="text-gray-600">
                        <?php echo $lang === 'ar' ? 'وصف تجريبي للمحتوى' : 'Test description content'; ?>
                    </p>
                </div>
            </div>
        </div>
        
        <div class="mt-8">
            <a href="index.php" class="bg-primary-500 text-white px-6 py-3 rounded-lg hover:bg-primary-600 transition-colors">
                <?php echo $lang === 'ar' ? 'العودة للصفحة الرئيسية' : 'Back to Homepage'; ?>
            </a>
        </div>
    </div>
    
    <script>
        console.log('Current language:', '<?php echo $lang; ?>');
        console.log('Is RTL:', <?php echo $isRTL ? 'true' : 'false'; ?>);
        console.log('HTML dir attribute:', document.documentElement.dir);
        console.log('HTML lang attribute:', document.documentElement.lang);
    </script>
</body>
</html>
