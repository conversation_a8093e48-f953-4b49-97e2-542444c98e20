<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Map Test</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <style>
        .map-state {
            fill: #e5e7eb;
            stroke: #ffffff;
            stroke-width: 2;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .map-state:hover {
            fill: #0ea5e9;
            stroke: #0284c7;
            stroke-width: 3;
        }
        
        .map-state.active {
            fill: #0284c7;
            stroke: #0369a1;
            stroke-width: 3;
        }
    </style>
</head>
<body class="bg-gray-50 p-8">
    
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold text-center mb-8 text-blue-600">
            🗺️ Simple Malaysia Map Test
        </h1>
        
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <h2 class="text-xl font-bold mb-4 text-center">Interactive Map</h2>
            
            <div class="flex justify-center">
                <svg width="600" height="400" viewBox="0 0 600 400" xmlns="http://www.w3.org/2000/svg">
                    <!-- Kuala Lumpur -->
                    <circle id="kuala-lumpur" class="map-state" data-state="kuala-lumpur" 
                            cx="220" cy="200" r="10" />
                    
                    <!-- Penang -->
                    <circle id="penang" class="map-state" data-state="penang" 
                            cx="160" cy="120" r="8" />
                    
                    <!-- Sabah -->
                    <path id="sabah" class="map-state" data-state="sabah" 
                          d="M 420 120 L 480 115 L 520 130 L 530 160 L 510 180 L 470 175 L 430 165 L 410 145 Z" />
                    
                    <!-- Sarawak -->
                    <path id="sarawak" class="map-state" data-state="sarawak" 
                          d="M 380 180 L 470 175 L 510 180 L 520 210 L 500 240 L 450 245 L 400 240 L 370 220 L 360 200 Z" />
                    
                    <!-- Johor -->
                    <path id="johor" class="map-state" data-state="johor" 
                          d="M 215 250 L 255 225 L 280 240 L 290 270 L 270 290 L 240 285 L 220 270 Z" />
                    
                    <!-- Labels -->
                    <text x="220" y="185" text-anchor="middle" class="text-sm font-semibold fill-gray-600">Kuala Lumpur</text>
                    <text x="160" y="110" text-anchor="middle" class="text-sm font-semibold fill-gray-600">Penang</text>
                    <text x="470" y="150" text-anchor="middle" class="text-sm font-semibold fill-gray-600">Sabah</text>
                    <text x="440" y="215" text-anchor="middle" class="text-sm font-semibold fill-gray-600">Sarawak</text>
                    <text x="250" y="270" text-anchor="middle" class="text-sm font-semibold fill-gray-600">Johor</text>
                </svg>
            </div>
            
            <div class="mt-4 text-center text-sm text-gray-600">
                Click on any state to see information
            </div>
        </div>
        
        <!-- Info Panel -->
        <div id="info-panel" class="bg-white rounded-lg shadow-lg p-6">
            <div class="text-center">
                <h3 class="text-xl font-bold text-gray-800 mb-4">Select a state from the map</h3>
                <p class="text-gray-600">Click on any state to learn about its tourist attractions.</p>
            </div>
        </div>
        
        <!-- Test Links -->
        <div class="mt-8 text-center">
            <div class="flex flex-wrap justify-center gap-4">
                <a href="malaysia-map.php" class="bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600 transition-colors">
                    Full Map Page
                </a>
                <a href="debug-map.php" class="bg-green-500 text-white px-6 py-3 rounded-lg hover:bg-green-600 transition-colors">
                    Debug Map
                </a>
                <a href="test-map.php" class="bg-purple-500 text-white px-6 py-3 rounded-lg hover:bg-purple-600 transition-colors">
                    Test Map
                </a>
                <a href="index.php" class="bg-gray-500 text-white px-6 py-3 rounded-lg hover:bg-gray-600 transition-colors">
                    Homepage
                </a>
            </div>
        </div>
        
        <!-- Status -->
        <div class="mt-8 bg-green-100 border border-green-400 text-green-700 px-6 py-4 rounded-lg">
            <div class="flex items-center">
                <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <div>
                    <h4 class="font-bold">Map Test Status</h4>
                    <p class="text-sm">If you can see this page and interact with the map, the basic functionality is working.</p>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        const stateInfo = {
            'kuala-lumpur': {
                name: 'Kuala Lumpur',
                description: 'The vibrant capital city of Malaysia with modern skyscrapers and cultural diversity.',
                attractions: ['Petronas Twin Towers', 'KL Tower', 'Batu Caves', 'Central Market']
            },
            'penang': {
                name: 'Penang',
                description: 'Pearl of the Orient, famous for UNESCO World Heritage George Town.',
                attractions: ['George Town', 'Penang Hill', 'Street Art', 'Clan Houses']
            },
            'sabah': {
                name: 'Sabah',
                description: 'Land Below the Wind, known for Mount Kinabalu and wildlife.',
                attractions: ['Mount Kinabalu', 'Sipadan Island', 'Orangutan Centre', 'Kinabatangan River']
            },
            'sarawak': {
                name: 'Sarawak',
                description: 'Land of the Hornbills, famous for caves and national parks.',
                attractions: ['Mulu Caves', 'Kuching Waterfront', 'Bako National Park', 'Longhouse Visits']
            },
            'johor': {
                name: 'Johor',
                description: 'Southern gateway to Malaysia with theme parks and beaches.',
                attractions: ['Legoland Malaysia', 'Sultan Abu Bakar Mosque', 'Desaru Beach', 'City Square']
            }
        };
        
        document.addEventListener('DOMContentLoaded', function() {
            const mapStates = document.querySelectorAll('.map-state');
            const infoPanel = document.getElementById('info-panel');
            
            console.log('✅ Simple map loaded');
            console.log('✅ Found', mapStates.length, 'states');
            
            mapStates.forEach(state => {
                state.addEventListener('click', function() {
                    const stateId = this.getAttribute('data-state');
                    console.log('Clicked:', stateId);
                    
                    // Remove active from all
                    mapStates.forEach(s => s.classList.remove('active'));
                    
                    // Add active to clicked
                    this.classList.add('active');
                    
                    // Show info
                    if (stateInfo[stateId]) {
                        const info = stateInfo[stateId];
                        infoPanel.innerHTML = `
                            <div class="text-center">
                                <h3 class="text-2xl font-bold text-blue-600 mb-4">${info.name}</h3>
                                <p class="text-gray-600 mb-4">${info.description}</p>
                                <div class="bg-blue-50 p-4 rounded-lg">
                                    <h4 class="font-semibold mb-2">Top Attractions:</h4>
                                    <ul class="text-sm text-gray-700">
                                        ${info.attractions.map(attraction => `<li>• ${attraction}</li>`).join('')}
                                    </ul>
                                </div>
                                <div class="mt-4 bg-green-100 p-3 rounded">
                                    <p class="text-green-600 text-sm">✅ Click functionality working!</p>
                                </div>
                            </div>
                        `;
                    }
                });
                
                state.addEventListener('mouseenter', function() {
                    if (!this.classList.contains('active')) {
                        this.style.fill = '#0ea5e9';
                    }
                });
                
                state.addEventListener('mouseleave', function() {
                    if (!this.classList.contains('active')) {
                        this.style.fill = '#e5e7eb';
                    }
                });
            });
        });
    </script>
</body>
</html>
