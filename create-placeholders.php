<?php
// Simple script to create placeholder images
function createPlaceholderImage($width, $height, $text, $filename) {
    $image = imagecreate($width, $height);
    
    // Colors
    $bg_color = imagecolorallocate($image, 200, 200, 200);
    $text_color = imagecolorallocate($image, 100, 100, 100);
    
    // Add text
    $font_size = 5;
    $text_width = imagefontwidth($font_size) * strlen($text);
    $text_height = imagefontheight($font_size);
    
    $x = ($width - $text_width) / 2;
    $y = ($height - $text_height) / 2;
    
    imagestring($image, $font_size, $x, $y, $text, $text_color);
    
    // Save image
    imagejpeg($image, $filename, 80);
    imagedestroy($image);
}

// Create placeholder images
$placeholders = [
    ['assets/images/logo.png', 200, 60, 'LOGO'],
    ['assets/images/logo-white.png', 200, 60, 'LOGO'],
    ['assets/images/company-intro.jpg', 600, 400, 'Company Introduction'],
    ['assets/images/destinations/kl-city.jpg', 400, 300, 'KL City Tour'],
    ['assets/images/destinations/langkawi.jpg', 400, 300, 'Langkawi Island'],
    ['assets/images/destinations/penang.jpg', 400, 300, 'Penang Heritage'],
    ['assets/images/articles/top-10-places.jpg', 400, 250, 'Top 10 Places'],
    ['assets/images/articles/malaysian-food.jpg', 400, 250, 'Malaysian Food'],
    ['assets/images/gallery/petronas-towers.jpg', 400, 300, 'Petronas Towers'],
    ['assets/images/gallery/langkawi-beach.jpg', 400, 300, 'Langkawi Beach'],
    ['assets/images/gallery/thumbs/petronas-towers.jpg', 200, 150, 'Petronas'],
    ['assets/images/gallery/thumbs/langkawi-beach.jpg', 200, 150, 'Beach'],
    ['assets/images/testimonials/ahmed.jpg', 100, 100, 'Ahmed'],
    ['assets/images/testimonials/sarah.jpg', 100, 100, 'Sarah'],
    ['assets/images/testimonials/omar.jpg', 100, 100, 'Omar']
];

echo "<h2>Creating placeholder images...</h2>";

foreach ($placeholders as $placeholder) {
    list($filename, $width, $height, $text) = $placeholder;
    
    // Create directory if it doesn't exist
    $dir = dirname($filename);
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
    
    createPlaceholderImage($width, $height, $text, $filename);
    echo "<p>Created: {$filename}</p>";
}

echo "<p style='color: green;'>✓ All placeholder images created successfully!</p>";
echo "<p><a href='setup.php'>Continue to Database Setup</a></p>";
?>

<!DOCTYPE html>
<html>
<head>
    <title>Create Placeholder Images</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }
        h2 { color: #0ea5e9; }
        p { margin: 10px 0; }
        a { color: #0ea5e9; text-decoration: none; }
        a:hover { text-decoration: underline; }
    </style>
</head>
<body>
