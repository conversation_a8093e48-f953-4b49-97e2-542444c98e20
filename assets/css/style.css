/* Custom CSS for Malaysia Tourism Website */

/* RTL Support */
[dir="rtl"] {
    text-align: right;
}

[dir="rtl"] .space-x-reverse > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 1;
}

/* Font Classes */
.font-arabic {
    font-family: 'IBM Plex Sans Arabic', sans-serif;
}

.font-english {
    font-family: 'Inter', sans-serif;
}

/* Line Clamp Utilities */
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Hero Section Animations */
.hero-title {
    animation: fadeInUp 1s ease-out 0.5s both;
}

.hero-subtitle {
    animation: fadeInUp 1s ease-out 0.8s both;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* FIXED: All content visible by default - NO OPACITY ISSUES */
.scroll-animate {
    opacity: 1 !important;
    transform: translateY(0) !important;
    transition: all 0.6s ease-out;
}

.scroll-animate.animate {
    opacity: 1 !important;
    transform: translateY(0) !important;
}

/* Force all animated content to be visible */
.scroll-animate,
.article-card,
.destination-card,
.gallery-item {
    opacity: 1 !important;
    transform: translateY(0) !important;
    visibility: visible !important;
}

/* Card Hover Effects */
.destination-card {
    transition: all 0.3s ease;
}

.destination-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.article-card {
    transition: all 0.3s ease;
}

.article-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.gallery-item {
    transition: all 0.3s ease;
}

.gallery-item:hover {
    transform: scale(1.05);
}

/* Testimonials Slider */
.testimonials-slider {
    position: relative;
    overflow: hidden;
}

.testimonials-container {
    overflow: hidden;
}

.testimonials-track {
    display: flex;
    transition: transform 0.5s ease;
}

.testimonial-slide {
    flex: 0 0 auto;
    width: 100%;
}

@media (min-width: 768px) {
    .testimonial-slide {
        width: 50%;
    }
}

@media (min-width: 1024px) {
    .testimonial-slide {
        width: 33.333333%;
    }
}

/* Navigation Styles */
.nav-link {
    position: relative;
    transition: color 0.2s ease;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    right: 0;
    height: 2px;
    background-color: transparent;
    transition: background-color 0.2s ease;
}

.nav-link:hover::after,
.nav-link.active::after {
    background-color: #0ea5e9;
}

/* Mobile Navigation */
.mobile-nav-link {
    display: block;
    padding: 0.75rem;
    color: #6b7280;
    font-weight: 500;
    border-radius: 0.375rem;
    transition: all 0.2s ease;
}

.mobile-nav-link:hover,
.mobile-nav-link.active {
    color: #0ea5e9;
    background-color: #f0f9ff;
}

/* Lightbox Styles */
#lightbox {
    backdrop-filter: blur(4px);
}

#lightbox-image {
    border-radius: 0.5rem;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

#lightbox-close {
    background: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s ease;
}

#lightbox-close:hover {
    background: rgba(0, 0, 0, 0.7);
}

/* Form Styles */
.form-input {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    font-size: 1rem;
    transition: all 0.2s ease;
    background-color: white;
}

.form-input:focus {
    outline: none;
    border-color: #0ea5e9;
    box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
}

.form-textarea {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    font-size: 1rem;
    resize: vertical;
    min-height: 120px;
    transition: all 0.2s ease;
    background-color: white;
}

.form-textarea:focus {
    outline: none;
    border-color: #0ea5e9;
    box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
}

.form-button {
    background-color: #0ea5e9;
    color: white;
    padding: 0.75rem 2rem;
    border-radius: 0.5rem;
    font-weight: 600;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.form-button:hover {
    background-color: #0284c7;
    transform: translateY(-1px);
}

.form-button:disabled {
    background-color: #9ca3af;
    cursor: not-allowed;
    transform: none;
}

/* Loading Spinner */
.spinner {
    border: 2px solid #f3f4f6;
    border-top: 2px solid #0ea5e9;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    animation: spin 1s linear infinite;
    margin-right: 0.5rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Gallery Filter Buttons */
.filter-button {
    padding: 0.5rem 1rem;
    border-radius: 9999px;
    font-weight: 500;
    transition: all 0.2s ease;
    border: 1px solid #d1d5db;
    background-color: white;
    color: #6b7280;
}

.filter-button:hover,
.filter-button.active {
    background-color: #0ea5e9;
    color: white;
    border-color: #0ea5e9;
}

/* Pagination Styles */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
    margin-top: 2rem;
}

.pagination-button {
    padding: 0.5rem 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    background-color: white;
    color: #6b7280;
    text-decoration: none;
    transition: all 0.2s ease;
}

.pagination-button:hover,
.pagination-button.active {
    background-color: #0ea5e9;
    color: white;
    border-color: #0ea5e9;
}

.pagination-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Search Bar */
.search-container {
    position: relative;
}

.search-input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 3rem;
    border: 1px solid #d1d5db;
    border-radius: 9999px;
    font-size: 1rem;
    transition: all 0.2s ease;
    background-color: white;
}

.search-input:focus {
    outline: none;
    border-color: #0ea5e9;
    box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
}

.search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #9ca3af;
    width: 1.25rem;
    height: 1.25rem;
}

/* Responsive Video */
.video-responsive {
    position: relative;
    padding-bottom: 56.25%; /* 16:9 aspect ratio */
    height: 0;
    overflow: hidden;
}

.video-responsive iframe,
.video-responsive video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Print Styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    body {
        font-size: 12pt;
        line-height: 1.4;
    }
    
    h1, h2, h3, h4, h5, h6 {
        page-break-after: avoid;
    }
    
    img {
        max-width: 100% !important;
        height: auto !important;
    }
}

/* Accessibility */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus Styles */
button:focus,
a:focus,
input:focus,
textarea:focus,
select:focus {
    outline: 2px solid #0ea5e9;
    outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .text-gray-600 {
        color: #000000;
    }
    
    .text-gray-300 {
        color: #ffffff;
    }
    
    .border-gray-200 {
        border-color: #000000;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}
