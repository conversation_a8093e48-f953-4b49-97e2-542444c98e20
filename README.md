# Malaysia Tourism Website

A bilingual (Arabic & English) dynamic tourism and travel website built with Tailwind CSS, JavaScript, and PHP, featuring a professional admin panel.

## Features

### 🌐 Bilingual Support
- Full Arabic (RTL) and English (LTR) support
- IBM Plex Sans Arabic font for Arabic text
- Inter font for English text
- Dynamic language switching

### 🎨 Modern Design
- Responsive design with Tailwind CSS
- GSAP-powered scroll animations
- Interactive sliders and hover effects
- Mobile-first approach

### 📱 Pages Included
- **Home Page**: 8 animated sections including hero, company intro, destinations, gallery, blog, testimonials, WhatsApp CTA, and newsletter
- **About Us**: Company story with timeline animations
- **Contact Us**: Dynamic form with WhatsApp integration and Google Maps
- **Photo Gallery**: Filterable gallery with lightbox modal
- **Blog/Articles**: Paginated articles with search functionality
- **Interactive Malaysia Map**: SVG map with clickable states and tourist information

### 🛠️ Technical Stack
- **Frontend**: Tailwind CSS, JavaScript, GSAP
- **Backend**: PHP 7.4+, MySQL
- **Features**: Custom admin panel, dynamic content management, responsive design

### 🔧 Admin Panel Features
- Modern, intuitive interface
- Manage articles, destinations, gallery images
- Contact form messages management
- Malaysia states/provinces data management
- User authentication system

## Installation

### Prerequisites
- XAMPP/WAMP/LAMP server
- PHP 7.4 or higher
- MySQL 5.7 or higher
- Web browser with JavaScript enabled

### Setup Instructions

1. **Clone/Download the project** to your web server directory:
   ```
   /Applications/XAMPP/xamppfiles/htdocs/travel/
   ```

2. **Create placeholder images** (run once):
   ```
   http://localhost/travel/create-placeholders.php
   ```

3. **Setup database and sample data**:
   ```
   http://localhost/travel/setup.php
   ```

4. **Access the website**:
   ```
   http://localhost/travel/
   ```

5. **Access admin panel**:
   ```
   http://localhost/travel/admin/login.php
   Username: admin
   Password: admin123
   ```

### Configuration

Edit `includes/config.php` to configure:
- Database connection settings
- Site URLs and contact information
- WhatsApp number
- Email settings

## File Structure

```
travel/
├── index.php                 # Homepage
├── setup.php                # Database setup script
├── create-placeholders.php   # Image placeholder generator
├── switch-language.php       # Language switching handler
├── includes/
│   ├── config.php           # Configuration settings
│   ├── Database.php         # Database connection class
│   ├── ContentManager.php   # Content management class
│   ├── header.php           # Site header
│   └── footer.php           # Site footer
├── assets/
│   ├── css/
│   │   └── style.css        # Custom CSS styles
│   ├── js/
│   │   └── main.js          # Main JavaScript file
│   ├── images/              # Image assets
│   └── videos/              # Video assets
├── api/
│   ├── contact.php          # Contact form API
│   └── newsletter.php       # Newsletter subscription API
├── admin/                   # Admin panel (to be developed)
└── uploads/                 # User uploaded files
    ├── gallery/
    ├── articles/
    └── testimonials/
```

## Database Schema

The system includes the following main tables:
- `admin_users` - Admin panel users
- `articles` - Blog articles (bilingual)
- `destinations` - Tourist destinations (bilingual)
- `gallery` - Photo gallery images
- `testimonials` - Customer reviews (bilingual)
- `contact_messages` - Contact form submissions
- `newsletter_subscribers` - Email subscribers
- `malaysia_states` - Malaysia states data (bilingual)
- `site_settings` - Site configuration

## Features in Detail

### Bilingual Content Management
- All content stored in both Arabic and English
- Dynamic language switching without page reload
- RTL/LTR layout support
- Localized date and number formatting

### Interactive Elements
- GSAP scroll animations
- Testimonials slider with auto-play
- Gallery lightbox with keyboard navigation
- Smooth scrolling and hover effects
- Mobile-responsive navigation

### Contact & Communication
- WhatsApp integration with floating button
- Contact form with email notifications
- Newsletter subscription system
- Social media links

### SEO & Performance
- Semantic HTML structure
- Optimized images with lazy loading
- Meta tags and structured data ready
- Fast loading with minified assets

## Customization

### Adding New Languages
1. Add language code to `SUPPORTED_LANGS` in `config.php`
2. Add translations to the `t()` function
3. Update database schema to include new language columns
4. Modify templates to support new language

### Styling Customization
- Edit `assets/css/style.css` for custom styles
- Modify Tailwind configuration in HTML templates
- Update color scheme in `tailwind.config`

### Adding New Features
- Create new PHP classes in `includes/`
- Add API endpoints in `api/`
- Extend database schema as needed
- Update admin panel accordingly

## Browser Support
- Chrome 60+
- Firefox 60+
- Safari 12+
- Edge 79+
- Mobile browsers (iOS Safari, Chrome Mobile)

## Security Features
- SQL injection protection with prepared statements
- XSS protection with input sanitization
- CSRF protection for forms
- Secure session management
- Input validation and filtering

## Performance Optimizations
- Lazy loading for images
- Minified CSS and JavaScript
- Database query optimization
- Caching headers for static assets
- Responsive image serving

## Contributing
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License
This project is proprietary software. All rights reserved.

## Support
For support and customization requests, please contact the development team.

---

**Note**: This is a complete tourism website solution with bilingual support, modern design, and comprehensive admin panel. The system is designed to be easily customizable and scalable for different tourism businesses.
