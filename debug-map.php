<?php
// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();

echo "<h2>Debug Information:</h2>";

// Check if config file exists
if (file_exists('includes/config.php')) {
    echo "✅ Config file exists<br>";
    require_once 'includes/config.php';
} else {
    echo "❌ Config file missing<br>";
    die();
}

// Check if Database class exists
if (file_exists('includes/Database.php')) {
    echo "✅ Database file exists<br>";
    require_once 'includes/Database.php';
} else {
    echo "❌ Database file missing<br>";
    die();
}

// Check if ContentManager class exists
if (file_exists('includes/ContentManager.php')) {
    echo "✅ ContentManager file exists<br>";
    require_once 'includes/ContentManager.php';
} else {
    echo "❌ ContentManager file missing<br>";
    die();
}

// Handle language switching
if (isset($_GET['lang']) && in_array($_GET['lang'], SUPPORTED_LANGS)) {
    $_SESSION['lang'] = $_GET['lang'];
}

$lang = $_SESSION['lang'] ?? DEFAULT_LANG;
$isRTL = $lang === 'ar';

echo "✅ Language: $lang<br>";
echo "✅ RTL: " . ($isRTL ? 'Yes' : 'No') . "<br>";

// Try to create database connection
try {
    $db = new Database();
    echo "✅ Database connection created<br>";
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br>";
    // Continue without database for testing
    $db = null;
}

// Try to create ContentManager
try {
    if ($db) {
        $contentManager = new ContentManager($db);
        echo "✅ ContentManager created<br>";
        
        // Try to get states
        $states = $contentManager->getMalaysiaStates($lang);
        echo "✅ States data retrieved: " . count($states) . " states<br>";
    } else {
        echo "⚠️ Skipping ContentManager due to database issue<br>";
        $states = [];
    }
} catch (Exception $e) {
    echo "❌ ContentManager error: " . $e->getMessage() . "<br>";
    $states = [];
}

?>

<!DOCTYPE html>
<html lang="<?php echo $lang; ?>" dir="<?php echo $isRTL ? 'rtl' : 'ltr'; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Map Debug</title>
    
    <!-- Fonts -->
    <?php if ($lang === 'ar'): ?>
        <link href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <?php else: ?>
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <?php endif; ?>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <style>
        .font-arabic { font-family: 'IBM Plex Sans Arabic', sans-serif; }
        .font-english { font-family: 'Inter', sans-serif; }
        
        .map-state {
            fill: #e5e7eb;
            stroke: #ffffff;
            stroke-width: 2;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .map-state:hover {
            fill: #0ea5e9;
            stroke: #0284c7;
            stroke-width: 3;
        }
        
        .map-state.active {
            fill: #0284c7;
            stroke: #0369a1;
            stroke-width: 3;
        }
        
        .scroll-animate,
        .article-card,
        .destination-card,
        .gallery-item {
            opacity: 1 !important;
            transform: translateY(0) !important;
            visibility: visible !important;
        }
    </style>
</head>
<body class="<?php echo $lang === 'ar' ? 'font-arabic' : 'font-english'; ?> bg-gray-50">
    
    <div class="max-w-7xl mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold text-center mb-8">
            🗺️ <?php echo $lang === 'ar' ? 'خريطة ماليزيا التفاعلية - تصحيح الأخطاء' : 'Interactive Malaysia Map - Debug'; ?>
        </h1>
        
        <div class="grid lg:grid-cols-3 gap-8">
            <!-- Map Container -->
            <div class="lg:col-span-2">
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <h3 class="text-xl font-bold text-gray-800 text-center mb-4">
                        <?php echo $lang === 'ar' ? 'خريطة ماليزيا' : 'Malaysia Map'; ?>
                    </h3>
                    
                    <!-- SVG Map -->
                    <div class="flex justify-center">
                        <svg class="malaysia-map" width="600" height="400" viewBox="0 0 600 400" xmlns="http://www.w3.org/2000/svg">
                            <!-- Kuala Lumpur -->
                            <circle id="kuala-lumpur" class="map-state" data-state="kuala-lumpur" 
                                    cx="220" cy="200" r="8" />
                            
                            <!-- Penang -->
                            <circle id="penang" class="map-state" data-state="penang" 
                                    cx="160" cy="120" r="8" />
                            
                            <!-- Sabah -->
                            <path id="sabah" class="map-state" data-state="sabah" 
                                  d="M 420 120 L 480 115 L 520 130 L 530 160 L 510 180 L 470 175 L 430 165 L 410 145 Z" />
                            
                            <!-- Sarawak -->
                            <path id="sarawak" class="map-state" data-state="sarawak" 
                                  d="M 380 180 L 470 175 L 510 180 L 520 210 L 500 240 L 450 245 L 400 240 L 370 220 L 360 200 Z" />
                            
                            <!-- Johor -->
                            <path id="johor" class="map-state" data-state="johor" 
                                  d="M 215 250 L 255 225 L 280 240 L 290 270 L 270 290 L 240 285 L 220 270 Z" />
                            
                            <!-- Labels -->
                            <text x="220" y="190" text-anchor="middle" class="text-sm font-semibold fill-gray-600">KL</text>
                            <text x="160" y="135" text-anchor="middle" class="text-sm font-semibold fill-gray-600">Penang</text>
                            <text x="470" y="150" text-anchor="middle" class="text-sm font-semibold fill-gray-600">Sabah</text>
                            <text x="440" y="215" text-anchor="middle" class="text-sm font-semibold fill-gray-600">Sarawak</text>
                            <text x="250" y="270" text-anchor="middle" class="text-sm font-semibold fill-gray-600">Johor</text>
                        </svg>
                    </div>
                </div>
            </div>
            
            <!-- State Information Panel -->
            <div class="lg:col-span-1">
                <div id="state-info" class="bg-white rounded-lg shadow-lg p-6 min-h-96">
                    <div id="default-info" class="text-center">
                        <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold text-gray-800 mb-4">
                            <?php echo $lang === 'ar' ? 'اختر ولاية من الخريطة' : 'Select a state from the map'; ?>
                        </h3>
                        <p class="text-gray-600">
                            <?php echo $lang === 'ar' ? 
                                'انقر على أي ولاية في الخريطة لمعرفة أهم المعالم السياحية فيها.' : 
                                'Click on any state in the map to learn about its top tourist attractions.'; ?>
                        </p>
                    </div>
                    
                    <div id="state-details" class="hidden">
                        <!-- State details will be populated by JavaScript -->
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Navigation -->
        <div class="mt-8 text-center">
            <div class="flex flex-wrap justify-center gap-4">
                <a href="malaysia-map.php?lang=<?php echo $lang; ?>" class="bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600 transition-colors">
                    <?php echo $lang === 'ar' ? 'الخريطة الأصلية' : 'Original Map'; ?>
                </a>
                <a href="test-map.php?lang=<?php echo $lang; ?>" class="bg-green-500 text-white px-6 py-3 rounded-lg hover:bg-green-600 transition-colors">
                    <?php echo $lang === 'ar' ? 'اختبار الخريطة' : 'Test Map'; ?>
                </a>
                <a href="index.php?lang=<?php echo $lang; ?>" class="bg-gray-500 text-white px-6 py-3 rounded-lg hover:bg-gray-600 transition-colors">
                    <?php echo $lang === 'ar' ? 'الصفحة الرئيسية' : 'Homepage'; ?>
                </a>
            </div>
        </div>
    </div>
    
    <script>
        // Simple state data
        const stateData = {
            'kuala-lumpur': {
                name: {
                    en: 'Kuala Lumpur',
                    ar: 'كوالالمبور'
                },
                description: {
                    en: 'The vibrant capital city of Malaysia',
                    ar: 'العاصمة النابضة بالحياة في ماليزيا'
                },
                attractions: [
                    { name: { en: 'Petronas Twin Towers', ar: 'برجا بتروناس التوأم' }, type: { en: 'Architecture', ar: 'عمارة' } },
                    { name: { en: 'KL Tower', ar: 'برج كوالالمبور' }, type: { en: 'Landmark', ar: 'معلم' } }
                ]
            },
            'penang': {
                name: { en: 'Penang', ar: 'بينانغ' },
                description: { en: 'Pearl of the Orient', ar: 'لؤلؤة الشرق' },
                attractions: [
                    { name: { en: 'George Town', ar: 'جورج تاون' }, type: { en: 'Heritage', ar: 'تراث' } },
                    { name: { en: 'Penang Hill', ar: 'تل بينانغ' }, type: { en: 'Nature', ar: 'طبيعة' } }
                ]
            }
        };
        
        const currentLang = '<?php echo $lang; ?>';
        
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🗺️ Debug map loaded');
            
            const mapStates = document.querySelectorAll('.map-state');
            console.log('Found', mapStates.length, 'clickable states');
            
            mapStates.forEach(state => {
                state.addEventListener('click', function() {
                    const stateId = this.getAttribute('data-state');
                    console.log('Clicked state:', stateId);
                    
                    // Remove active from all
                    mapStates.forEach(s => s.classList.remove('active'));
                    
                    // Add active to clicked
                    this.classList.add('active');
                    
                    // Show info
                    showStateInfo(stateId);
                });
            });
        });
        
        function showStateInfo(stateId) {
            const defaultInfo = document.getElementById('default-info');
            const stateDetails = document.getElementById('state-details');
            
            if (stateData[stateId]) {
                const data = stateData[stateId];
                
                defaultInfo.classList.add('hidden');
                stateDetails.classList.remove('hidden');
                
                stateDetails.innerHTML = `
                    <div class="text-center">
                        <h3 class="text-2xl font-bold text-blue-600 mb-4">${data.name[currentLang]}</h3>
                        <p class="text-gray-600 mb-4">${data.description[currentLang]}</p>
                        <div class="space-y-2">
                            ${data.attractions.map(attraction => `
                                <div class="bg-gray-50 p-3 rounded">
                                    <div class="font-semibold">${attraction.name[currentLang]}</div>
                                    <div class="text-sm text-gray-500">${attraction.type[currentLang]}</div>
                                </div>
                            `).join('')}
                        </div>
                        <div class="mt-4 bg-green-100 p-3 rounded">
                            <p class="text-green-600 text-sm">✅ ${currentLang === 'ar' ? 'يعمل بشكل صحيح!' : 'Working correctly!'}</p>
                        </div>
                    </div>
                `;
            } else {
                defaultInfo.classList.add('hidden');
                stateDetails.classList.remove('hidden');
                
                stateDetails.innerHTML = `
                    <div class="text-center">
                        <h3 class="text-xl font-bold text-gray-800 mb-4">${stateId}</h3>
                        <p class="text-gray-600">${currentLang === 'ar' ? 'معلومات قريباً' : 'Information coming soon'}</p>
                        <div class="mt-4 bg-yellow-100 p-3 rounded">
                            <p class="text-yellow-600 text-sm">⚠️ ${currentLang === 'ar' ? 'لا توجد بيانات' : 'No data available'}</p>
                        </div>
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
