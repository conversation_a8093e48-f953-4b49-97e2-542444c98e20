<?php
require_once 'includes/config.php';
require_once 'includes/Database.php';

// Create database connection
try {
    $db = new Database();
    echo "<h2>Setting up Malaysia Tourism Website Database...</h2>";
    
    // Create tables
    echo "<p>Creating database tables...</p>";
    $db->createTables();
    echo "<p style='color: green;'>✓ Database tables created successfully!</p>";
    
    // Insert sample data
    echo "<p>Inserting sample data...</p>";
    insertSampleData($db);
    echo "<p style='color: green;'>✓ Sample data inserted successfully!</p>";
    
    echo "<h3 style='color: green;'>Setup completed successfully!</h3>";
    echo "<p><strong>Admin Login Details:</strong></p>";
    echo "<p>Username: admin</p>";
    echo "<p>Password: admin123</p>";
    echo "<p><a href='admin/login.php'>Go to Admin Panel</a></p>";
    echo "<p><a href='index.php'>View Website</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

function insertSampleData($db) {
    // Insert sample destinations
    $destinations = [
        [
            'name_en' => 'Kuala Lumpur City Tour',
            'name_ar' => 'جولة مدينة كوالالمبور',
            'slug_en' => 'kuala-lumpur-city-tour',
            'slug_ar' => 'جولة-مدينة-كوالالمبور',
            'description_en' => 'Explore the vibrant capital city of Malaysia with its iconic landmarks, shopping districts, and cultural attractions.',
            'description_ar' => 'استكشف العاصمة النابضة بالحياة في ماليزيا مع معالمها الشهيرة ومناطق التسوق والمعالم الثقافية.',
            'image' => 'assets/images/destinations/kl-city.jpg',
            'price' => '$299',
            'duration_en' => '3 Days / 2 Nights',
            'duration_ar' => '3 أيام / ليلتان',
            'location_en' => 'Kuala Lumpur',
            'location_ar' => 'كوالالمبور',
            'highlights_en' => 'Petronas Twin Towers, KL Tower, Batu Caves, Central Market',
            'highlights_ar' => 'برجا بتروناس التوأم، برج كوالالمبور، كهوف باتو، السوق المركزي',
            'is_featured' => 1
        ],
        [
            'name_en' => 'Langkawi Island Paradise',
            'name_ar' => 'جنة جزيرة لنكاوي',
            'slug_en' => 'langkawi-island-paradise',
            'slug_ar' => 'جنة-جزيرة-لنكاوي',
            'description_en' => 'Discover the beautiful beaches, crystal clear waters, and tropical paradise of Langkawi Island.',
            'description_ar' => 'اكتشف الشواطئ الجميلة والمياه الصافية والجنة الاستوائية في جزيرة لنكاوي.',
            'image' => 'assets/images/destinations/langkawi.jpg',
            'price' => '$399',
            'duration_en' => '4 Days / 3 Nights',
            'duration_ar' => '4 أيام / 3 ليال',
            'location_en' => 'Langkawi',
            'location_ar' => 'لنكاوي',
            'highlights_en' => 'Cable Car, Underwater World, Eagle Square, Beach Resorts',
            'highlights_ar' => 'التلفريك، العالم تحت الماء، ميدان النسر، منتجعات الشاطئ',
            'is_featured' => 1
        ],
        [
            'name_en' => 'Penang Heritage Tour',
            'name_ar' => 'جولة تراث بينانغ',
            'slug_en' => 'penang-heritage-tour',
            'slug_ar' => 'جولة-تراث-بينانغ',
            'description_en' => 'Experience the rich cultural heritage and delicious street food of George Town, Penang.',
            'description_ar' => 'اختبر التراث الثقافي الغني والطعام الشعبي اللذيذ في جورج تاون، بينانغ.',
            'image' => 'assets/images/destinations/penang.jpg',
            'price' => '$349',
            'duration_en' => '3 Days / 2 Nights',
            'duration_ar' => '3 أيام / ليلتان',
            'location_en' => 'Penang',
            'location_ar' => 'بينانغ',
            'highlights_en' => 'George Town UNESCO Site, Street Art, Local Food, Clan Houses',
            'highlights_ar' => 'موقع جورج تاون اليونسكو، فن الشارع، الطعام المحلي، بيوت العشائر',
            'is_featured' => 1
        ]
    ];
    
    foreach ($destinations as $destination) {
        $db->insert('destinations', $destination);
    }
    
    // Insert sample articles
    $articles = [
        [
            'title_en' => 'Top 10 Must-Visit Places in Malaysia',
            'title_ar' => 'أفضل 10 أماكن يجب زيارتها في ماليزيا',
            'slug_en' => 'top-10-must-visit-places-malaysia',
            'slug_ar' => 'أفضل-10-أماكن-يجب-زيارتها-في-ماليزيا',
            'excerpt_en' => 'Discover the most beautiful and exciting destinations that Malaysia has to offer.',
            'excerpt_ar' => 'اكتشف أجمل وأروع الوجهات التي تقدمها ماليزيا.',
            'content_en' => 'Malaysia is a diverse country with stunning landscapes, rich culture, and amazing attractions...',
            'content_ar' => 'ماليزيا بلد متنوع بمناظر طبيعية خلابة وثقافة غنية ومعالم مذهلة...',
            'featured_image' => 'assets/images/articles/top-10-places.jpg',
            'category_en' => 'Travel Guide',
            'category_ar' => 'دليل السفر',
            'is_featured' => 1,
            'author_id' => 1
        ],
        [
            'title_en' => 'Malaysian Cuisine: A Food Lover\'s Paradise',
            'title_ar' => 'المطبخ الماليزي: جنة عشاق الطعام',
            'slug_en' => 'malaysian-cuisine-food-lovers-paradise',
            'slug_ar' => 'المطبخ-الماليزي-جنة-عشاق-الطعام',
            'excerpt_en' => 'Explore the diverse and delicious world of Malaysian cuisine.',
            'excerpt_ar' => 'استكشف عالم المطبخ الماليزي المتنوع واللذيذ.',
            'content_en' => 'Malaysian cuisine is a melting pot of flavors from various cultures...',
            'content_ar' => 'المطبخ الماليزي هو بوتقة انصهار للنكهات من ثقافات مختلفة...',
            'featured_image' => 'assets/images/articles/malaysian-food.jpg',
            'category_en' => 'Food & Culture',
            'category_ar' => 'الطعام والثقافة',
            'is_featured' => 1,
            'author_id' => 1
        ]
    ];
    
    foreach ($articles as $article) {
        $db->insert('articles', $article);
    }
    
    // Insert sample testimonials
    $testimonials = [
        [
            'name' => 'Ahmed Al-Rashid',
            'review_en' => 'Amazing experience! The tour was well organized and the guide was very knowledgeable.',
            'review_ar' => 'تجربة مذهلة! كانت الجولة منظمة بشكل جيد والمرشد كان على دراية كبيرة.',
            'rating' => 5,
            'avatar' => 'assets/images/testimonials/ahmed.jpg',
            'location_en' => 'Dubai, UAE',
            'location_ar' => 'دبي، الإمارات',
            'is_featured' => 1
        ],
        [
            'name' => 'Sarah Johnson',
            'review_en' => 'Malaysia is absolutely beautiful! Thank you for making our trip unforgettable.',
            'review_ar' => 'ماليزيا جميلة جداً! شكراً لكم لجعل رحلتنا لا تُنسى.',
            'rating' => 5,
            'avatar' => 'assets/images/testimonials/sarah.jpg',
            'location_en' => 'London, UK',
            'location_ar' => 'لندن، المملكة المتحدة',
            'is_featured' => 1
        ],
        [
            'name' => 'Omar Hassan',
            'review_en' => 'Professional service and excellent value for money. Highly recommended!',
            'review_ar' => 'خدمة مهنية وقيمة ممتازة مقابل المال. أنصح بشدة!',
            'rating' => 5,
            'avatar' => 'assets/images/testimonials/omar.jpg',
            'location_en' => 'Cairo, Egypt',
            'location_ar' => 'القاهرة، مصر',
            'is_featured' => 1
        ]
    ];
    
    foreach ($testimonials as $testimonial) {
        $db->insert('testimonials', $testimonial);
    }
    
    // Insert sample gallery images
    $galleryImages = [
        [
            'title_en' => 'Petronas Twin Towers',
            'title_ar' => 'برجا بتروناس التوأم',
            'description_en' => 'Iconic twin towers in Kuala Lumpur',
            'description_ar' => 'البرجان التوأم الشهيران في كوالالمبور',
            'image' => 'assets/images/gallery/petronas-towers.jpg',
            'thumbnail' => 'assets/images/gallery/thumbs/petronas-towers.jpg',
            'category_en' => 'Architecture',
            'category_ar' => 'العمارة',
            'is_featured' => 1
        ],
        [
            'title_en' => 'Langkawi Beach',
            'title_ar' => 'شاطئ لنكاوي',
            'description_en' => 'Beautiful tropical beach in Langkawi',
            'description_ar' => 'شاطئ استوائي جميل في لنكاوي',
            'image' => 'assets/images/gallery/langkawi-beach.jpg',
            'thumbnail' => 'assets/images/gallery/thumbs/langkawi-beach.jpg',
            'category_en' => 'Beaches',
            'category_ar' => 'الشواطئ',
            'is_featured' => 1
        ]
    ];
    
    foreach ($galleryImages as $image) {
        $db->insert('gallery', $image);
    }
    
    // Insert Malaysia states data
    $states = [
        [
            'name_en' => 'Kuala Lumpur',
            'name_ar' => 'كوالالمبور',
            'code' => 'KL',
            'description_en' => 'The capital city of Malaysia, known for its modern skyline and cultural diversity.',
            'description_ar' => 'العاصمة الماليزية المعروفة بأفقها الحديث وتنوعها الثقافي.',
            'highlights_en' => 'Petronas Twin Towers, KL Tower, Bukit Bintang, Chinatown',
            'highlights_ar' => 'برجا بتروناس التوأم، برج كوالالمبور، بوكيت بينتانغ، الحي الصيني',
            'coordinates' => '3.139,101.6869'
        ],
        [
            'name_en' => 'Selangor',
            'name_ar' => 'سلانجور',
            'code' => 'SEL',
            'description_en' => 'The most developed state in Malaysia, surrounding Kuala Lumpur.',
            'description_ar' => 'الولاية الأكثر تطوراً في ماليزيا، تحيط بكوالالمبور.',
            'highlights_en' => 'Shah Alam, Klang, Batu Caves, Sunway Lagoon',
            'highlights_ar' => 'شاه علم، كلانغ، كهوف باتو، بحيرة صنواي',
            'coordinates' => '3.0738,101.5183'
        ]
    ];
    
    foreach ($states as $state) {
        $db->insert('malaysia_states', $state);
    }
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Malaysia Tourism Website - Setup</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }
        h2 { color: #0ea5e9; }
        p { margin: 10px 0; }
        a { color: #0ea5e9; text-decoration: none; }
        a:hover { text-decoration: underline; }
    </style>
</head>
<body>
