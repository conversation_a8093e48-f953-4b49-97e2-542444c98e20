<?php
session_start();
require_once 'includes/config.php';
require_once 'includes/Database.php';
require_once 'includes/ContentManager.php';

// Handle language switching
if (isset($_GET['lang']) && in_array($_GET['lang'], SUPPORTED_LANGS)) {
    $_SESSION['lang'] = $_GET['lang'];
}

$db = new Database();
$contentManager = new ContentManager($db);

// Get current language
$lang = $_SESSION['lang'] ?? DEFAULT_LANG;
$isRTL = $lang === 'ar';

// Pagination settings
$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$limit = ARTICLES_PER_PAGE;
$offset = ($page - 1) * $limit;

// Search functionality
$searchQuery = isset($_GET['search']) ? trim($_GET['search']) : '';
$category = isset($_GET['category']) ? trim($_GET['category']) : '';

// Get articles
if (!empty($searchQuery)) {
    $articles = $contentManager->searchArticles($searchQuery, $lang, $limit, $offset);
    $totalArticles = count($contentManager->searchArticles($searchQuery, $lang, 1000, 0)); // Get total for pagination
} else {
    $articles = $contentManager->getArticles($lang, $limit, $offset);
    $totalArticles = $contentManager->getStats()['total_articles'];
}

// Get featured articles
$featuredArticles = $contentManager->getFeaturedArticles($lang, 3);

// Calculate pagination
$totalPages = ceil($totalArticles / $limit);
?>

<!DOCTYPE html>
<html lang="<?php echo $lang; ?>" dir="<?php echo $isRTL ? 'rtl' : 'ltr'; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $lang === 'ar' ? 'المدونة - رحلات ماليزيا السياحية' : 'Blog - Malaysia Tourism Trips'; ?></title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <?php if ($lang === 'ar'): ?>
        <link href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <?php else: ?>
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <?php endif; ?>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'arabic': ['IBM Plex Sans Arabic', 'sans-serif'],
                        'english': ['Inter', 'sans-serif']
                    },
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1'
                        },
                        secondary: {
                            500: '#f59e0b',
                            600: '#d97706'
                        }
                    }
                }
            }
        }
    </script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    
    <!-- GSAP for animations -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js"></script>
</head>
<body class="<?php echo $lang === 'ar' ? 'font-arabic' : 'font-english'; ?> bg-gray-50">
    
    <?php include 'includes/header.php'; ?>
    
    <main class="pt-16">
        <!-- Hero Section -->
        <section class="relative h-96 bg-gradient-to-r from-primary-600 to-primary-800 overflow-hidden">
            <div class="absolute inset-0 bg-black bg-opacity-30"></div>
            <div class="relative z-10 flex items-center justify-center h-full text-white text-center">
                <div class="max-w-4xl mx-auto px-4">
                    <h1 class="text-5xl md:text-6xl font-bold mb-4 hero-title">
                        <?php echo $lang === 'ar' ? 'مدونة السفر' : 'Travel Blog'; ?>
                    </h1>
                    <p class="text-xl md:text-2xl hero-subtitle">
                        <?php echo $lang === 'ar' ? 'اكتشف أحدث المقالات والنصائح السياحية' : 'Discover the latest articles and travel tips'; ?>
                    </p>
                </div>
            </div>
        </section>

        <!-- Search & Filter Section -->
        <section class="py-12 bg-white">
            <div class="max-w-7xl mx-auto px-4">
                <div class="flex flex-col lg:flex-row gap-6 items-center justify-between">
                    <!-- Search Bar -->
                    <div class="w-full lg:w-1/2">
                        <form method="GET" class="search-container">
                            <input type="hidden" name="lang" value="<?php echo $lang; ?>">
                            <input type="text" 
                                   name="search" 
                                   value="<?php echo htmlspecialchars($searchQuery); ?>"
                                   placeholder="<?php echo $lang === 'ar' ? 'ابحث في المقالات...' : 'Search articles...'; ?>"
                                   class="search-input">
                            <svg class="search-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </form>
                    </div>
                    
                    <!-- Results Info -->
                    <div class="text-gray-600">
                        <?php if (!empty($searchQuery)): ?>
                            <p><?php echo $lang === 'ar' ? 'نتائج البحث عن:' : 'Search results for:'; ?> 
                               <strong>"<?php echo htmlspecialchars($searchQuery); ?>"</strong>
                               (<?php echo $totalArticles; ?> <?php echo $lang === 'ar' ? 'نتيجة' : 'results'; ?>)
                            </p>
                        <?php else: ?>
                            <p><?php echo $totalArticles; ?> <?php echo $lang === 'ar' ? 'مقال' : 'articles'; ?></p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </section>

        <!-- Featured Articles Section -->
        <?php if (!empty($featuredArticles) && empty($searchQuery)): ?>
        <section class="py-16 bg-gray-100">
            <div class="max-w-7xl mx-auto px-4">
                <div class="text-center mb-12 scroll-animate">
                    <h2 class="text-3xl font-bold mb-4 text-gray-800">
                        <?php echo $lang === 'ar' ? 'المقالات المميزة' : 'Featured Articles'; ?>
                    </h2>
                    <p class="text-lg text-gray-600">
                        <?php echo $lang === 'ar' ? 'أهم المقالات والنصائح السياحية' : 'Top articles and travel tips'; ?>
                    </p>
                </div>
                
                <div class="grid md:grid-cols-3 gap-8">
                    <?php foreach ($featuredArticles as $article): ?>
                        <article class="bg-white rounded-lg shadow-lg overflow-hidden article-card scroll-animate">
                            <div class="aspect-w-16 aspect-h-9">
                                <img src="<?php echo $article['featured_image'] ?? 'assets/images/articles/default.jpg'; ?>" 
                                     alt="<?php echo htmlspecialchars($article['title']); ?>"
                                     class="w-full h-48 object-cover">
                            </div>
                            <div class="p-6">
                                <?php if (!empty($article['category'])): ?>
                                    <span class="inline-block px-3 py-1 bg-primary-100 text-primary-600 text-sm font-semibold rounded-full mb-3">
                                        <?php echo $article['category']; ?>
                                    </span>
                                <?php endif; ?>
                                <h3 class="text-xl font-bold mb-3 text-gray-800 line-clamp-2">
                                    <a href="article.php?slug=<?php echo $article['slug']; ?>&lang=<?php echo $lang; ?>" 
                                       class="hover:text-primary-500 transition-colors">
                                        <?php echo $article['title']; ?>
                                    </a>
                                </h3>
                                <p class="text-gray-600 mb-4 line-clamp-3">
                                    <?php echo $article['excerpt']; ?>
                                </p>
                                <div class="flex items-center justify-between text-sm text-gray-500">
                                    <span><?php echo formatDate($article['created_at'], $lang); ?></span>
                                    <span><?php echo $article['views']; ?> <?php echo $lang === 'ar' ? 'مشاهدة' : 'views'; ?></span>
                                </div>
                                <a href="article.php?slug=<?php echo $article['slug']; ?>&lang=<?php echo $lang; ?>" 
                                   class="inline-flex items-center mt-4 text-primary-500 hover:text-primary-600 font-semibold">
                                    <?php echo $lang === 'ar' ? 'اقرأ المزيد' : 'Read More'; ?>
                                    <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                    </svg>
                                </a>
                            </div>
                        </article>
                    <?php endforeach; ?>
                </div>
            </div>
        </section>
        <?php endif; ?>

        <!-- Articles Grid -->
        <section class="py-16 bg-white">
            <div class="max-w-7xl mx-auto px-4">
                <div class="text-center mb-12 scroll-animate">
                    <h2 class="text-3xl font-bold mb-4 text-gray-800">
                        <?php echo !empty($searchQuery) ? 
                            ($lang === 'ar' ? 'نتائج البحث' : 'Search Results') : 
                            ($lang === 'ar' ? 'جميع المقالات' : 'All Articles'); ?>
                    </h2>
                </div>
                
                <?php if (!empty($articles)): ?>
                    <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                        <?php foreach ($articles as $article): ?>
                            <article class="bg-white rounded-lg shadow-lg overflow-hidden article-card scroll-animate">
                                <div class="aspect-w-16 aspect-h-9">
                                    <img src="<?php echo $article['featured_image'] ?? 'assets/images/articles/default.jpg'; ?>" 
                                         alt="<?php echo htmlspecialchars($article['title']); ?>"
                                         class="w-full h-48 object-cover">
                                </div>
                                <div class="p-6">
                                    <?php if (!empty($article['category'])): ?>
                                        <span class="inline-block px-3 py-1 bg-primary-100 text-primary-600 text-sm font-semibold rounded-full mb-3">
                                            <?php echo $article['category']; ?>
                                        </span>
                                    <?php endif; ?>
                                    <h3 class="text-xl font-bold mb-3 text-gray-800 line-clamp-2">
                                        <a href="article.php?slug=<?php echo $article['slug']; ?>&lang=<?php echo $lang; ?>" 
                                           class="hover:text-primary-500 transition-colors">
                                            <?php echo $article['title']; ?>
                                        </a>
                                    </h3>
                                    <p class="text-gray-600 mb-4 line-clamp-3">
                                        <?php echo $article['excerpt']; ?>
                                    </p>
                                    <div class="flex items-center justify-between text-sm text-gray-500 mb-4">
                                        <span><?php echo formatDate($article['created_at'], $lang); ?></span>
                                        <span><?php echo $article['views']; ?> <?php echo $lang === 'ar' ? 'مشاهدة' : 'views'; ?></span>
                                    </div>
                                    <a href="article.php?slug=<?php echo $article['slug']; ?>&lang=<?php echo $lang; ?>" 
                                       class="inline-flex items-center text-primary-500 hover:text-primary-600 font-semibold">
                                        <?php echo $lang === 'ar' ? 'اقرأ المزيد' : 'Read More'; ?>
                                        <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                        </svg>
                                    </a>
                                </div>
                            </article>
                        <?php endforeach; ?>
                    </div>
                    
                    <!-- Pagination -->
                    <?php if ($totalPages > 1): ?>
                        <div class="pagination mt-12">
                            <?php if ($page > 1): ?>
                                <a href="?page=<?php echo $page - 1; ?>&search=<?php echo urlencode($searchQuery); ?>&lang=<?php echo $lang; ?>" 
                                   class="pagination-button">
                                    <?php echo $lang === 'ar' ? 'السابق' : 'Previous'; ?>
                                </a>
                            <?php endif; ?>
                            
                            <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                                <a href="?page=<?php echo $i; ?>&search=<?php echo urlencode($searchQuery); ?>&lang=<?php echo $lang; ?>" 
                                   class="pagination-button <?php echo $i === $page ? 'active' : ''; ?>">
                                    <?php echo $i; ?>
                                </a>
                            <?php endfor; ?>
                            
                            <?php if ($page < $totalPages): ?>
                                <a href="?page=<?php echo $page + 1; ?>&search=<?php echo urlencode($searchQuery); ?>&lang=<?php echo $lang; ?>" 
                                   class="pagination-button">
                                    <?php echo $lang === 'ar' ? 'التالي' : 'Next'; ?>
                                </a>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                    
                <?php else: ?>
                    <!-- No Articles Found -->
                    <div class="text-center py-16">
                        <svg class="w-24 h-24 mx-auto text-gray-300 mb-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <h3 class="text-2xl font-bold text-gray-800 mb-4">
                            <?php echo $lang === 'ar' ? 'لا توجد مقالات' : 'No Articles Found'; ?>
                        </h3>
                        <p class="text-gray-600 mb-8">
                            <?php echo !empty($searchQuery) ? 
                                ($lang === 'ar' ? 'لم نجد أي مقالات تطابق بحثك. جرب كلمات مختلفة.' : 'We couldn\'t find any articles matching your search. Try different keywords.') :
                                ($lang === 'ar' ? 'لا توجد مقالات متاحة حالياً.' : 'No articles are currently available.'); ?>
                        </p>
                        <?php if (!empty($searchQuery)): ?>
                            <a href="blog.php?lang=<?php echo $lang; ?>" 
                               class="bg-primary-500 hover:bg-primary-600 text-white px-6 py-3 rounded-lg font-semibold transition-colors">
                                <?php echo $lang === 'ar' ? 'عرض جميع المقالات' : 'View All Articles'; ?>
                            </a>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>
        </section>

        <!-- Newsletter CTA -->
        <section class="py-20 bg-primary-600 text-white">
            <div class="max-w-4xl mx-auto px-4 text-center">
                <h2 class="text-4xl font-bold mb-6">
                    <?php echo $lang === 'ar' ? 'اشترك في نشرتنا الإخبارية' : 'Subscribe to Our Newsletter'; ?>
                </h2>
                <p class="text-xl mb-8 opacity-90">
                    <?php echo $lang === 'ar' ? 
                        'احصل على أحدث المقالات والعروض السياحية مباشرة في بريدك الإلكتروني' : 
                        'Get the latest articles and travel offers directly in your email'; ?>
                </p>
                <form id="newsletter-form" class="flex flex-col sm:flex-row gap-4 justify-center max-w-md mx-auto">
                    <input type="email" 
                           name="email" 
                           required
                           placeholder="<?php echo $lang === 'ar' ? 'أدخل بريدك الإلكتروني' : 'Enter your email'; ?>"
                           class="flex-1 px-4 py-3 rounded-lg text-gray-800 focus:outline-none focus:ring-2 focus:ring-white">
                    <button type="submit" 
                            class="bg-white text-primary-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                        <?php echo $lang === 'ar' ? 'اشترك' : 'Subscribe'; ?>
                    </button>
                </form>
            </div>
        </section>
    </main>
    
    <?php include 'includes/footer.php'; ?>
    
    <!-- Custom JavaScript -->
    <script src="assets/js/main.js"></script>
</body>
</html>
