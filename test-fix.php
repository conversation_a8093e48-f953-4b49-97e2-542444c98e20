<?php
session_start();
require_once 'includes/config.php';

$lang = $_SESSION['lang'] ?? DEFAULT_LANG;
$isRTL = $lang === 'ar';
?>

<!DOCTYPE html>
<html lang="<?php echo $lang; ?>" dir="<?php echo $isRTL ? 'rtl' : 'ltr'; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Opacity Fix Test</title>
    
    <!-- Fonts -->
    <?php if ($lang === 'ar'): ?>
        <link href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <?php else: ?>
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <?php endif; ?>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    
    <!-- IMMEDIATE FIX: Force content visibility -->
    <style>
        .scroll-animate,
        .article-card,
        .destination-card,
        .gallery-item {
            opacity: 1 !important;
            transform: translateY(0) !important;
            visibility: visible !important;
        }
        
        .font-arabic {
            font-family: 'IBM Plex Sans Arabic', sans-serif;
        }
        
        .font-english {
            font-family: 'Inter', sans-serif;
        }
    </style>
</head>
<body class="<?php echo $lang === 'ar' ? 'font-arabic' : 'font-english'; ?> bg-gray-50 p-8">
    
    <div class="max-w-4xl mx-auto">
        <!-- Header -->
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold text-green-600 mb-4">
                ✅ <?php echo $lang === 'ar' ? 'تم إصلاح مشكلة الشفافية!' : 'Opacity Issue Fixed!'; ?>
            </h1>
            <p class="text-lg text-gray-600">
                <?php echo $lang === 'ar' ? 'جميع المحتويات يجب أن تكون مرئية الآن' : 'All content should now be visible'; ?>
            </p>
        </div>
        
        <!-- Test Content with scroll-animate class -->
        <div class="space-y-8">
            <div class="scroll-animate bg-white p-8 rounded-lg shadow-lg">
                <h2 class="text-2xl font-bold mb-4 text-blue-600">
                    <?php echo $lang === 'ar' ? 'اختبار 1: محتوى مع فئة scroll-animate' : 'Test 1: Content with scroll-animate class'; ?>
                </h2>
                <p class="text-gray-700">
                    <?php echo $lang === 'ar' ? 
                        'هذا المحتوى يحتوي على فئة scroll-animate ويجب أن يكون مرئياً بوضوح.' : 
                        'This content has the scroll-animate class and should be clearly visible.'; ?>
                </p>
                <div class="mt-4 p-4 bg-green-100 rounded">
                    <strong><?php echo $lang === 'ar' ? 'الحالة:' : 'Status:'; ?></strong> 
                    <span class="text-green-600"><?php echo $lang === 'ar' ? 'مرئي ✓' : 'Visible ✓'; ?></span>
                </div>
            </div>
            
            <div class="scroll-animate bg-white p-8 rounded-lg shadow-lg">
                <h2 class="text-2xl font-bold mb-4 text-purple-600">
                    <?php echo $lang === 'ar' ? 'اختبار 2: بطاقات متعددة' : 'Test 2: Multiple Cards'; ?>
                </h2>
                
                <div class="grid md:grid-cols-3 gap-6">
                    <div class="article-card bg-blue-50 p-6 rounded-lg">
                        <h3 class="font-bold mb-2"><?php echo $lang === 'ar' ? 'بطاقة مقال' : 'Article Card'; ?></h3>
                        <p class="text-sm text-gray-600"><?php echo $lang === 'ar' ? 'محتوى تجريبي' : 'Test content'; ?></p>
                    </div>
                    
                    <div class="destination-card bg-green-50 p-6 rounded-lg">
                        <h3 class="font-bold mb-2"><?php echo $lang === 'ar' ? 'بطاقة وجهة' : 'Destination Card'; ?></h3>
                        <p class="text-sm text-gray-600"><?php echo $lang === 'ar' ? 'محتوى تجريبي' : 'Test content'; ?></p>
                    </div>
                    
                    <div class="gallery-item bg-yellow-50 p-6 rounded-lg">
                        <h3 class="font-bold mb-2"><?php echo $lang === 'ar' ? 'عنصر معرض' : 'Gallery Item'; ?></h3>
                        <p class="text-sm text-gray-600"><?php echo $lang === 'ar' ? 'محتوى تجريبي' : 'Test content'; ?></p>
                    </div>
                </div>
            </div>
            
            <div class="scroll-animate bg-white p-8 rounded-lg shadow-lg">
                <h2 class="text-2xl font-bold mb-4 text-red-600">
                    <?php echo $lang === 'ar' ? 'اختبار 3: نص طويل' : 'Test 3: Long Text'; ?>
                </h2>
                <div class="prose max-w-none">
                    <p class="mb-4">
                        <?php echo $lang === 'ar' ? 
                            'هذا نص طويل لاختبار كيفية ظهور المحتوى النصي. يجب أن يكون هذا النص مرئياً بوضوح تام دون أي مشاكل في الشفافية أو الموضع. إذا كنت تستطيع قراءة هذا النص بوضوح، فإن الإصلاح يعمل بشكل صحيح.' : 
                            'This is a long text to test how text content appears. This text should be clearly visible without any opacity or positioning issues. If you can read this text clearly, the fix is working correctly.'; ?>
                    </p>
                    <p class="mb-4">
                        <?php echo $lang === 'ar' ? 
                            'فقرة أخرى من النص التجريبي. جميع الفقرات والعناصر يجب أن تكون مرئية فوراً عند تحميل الصفحة.' : 
                            'Another paragraph of test text. All paragraphs and elements should be visible immediately when the page loads.'; ?>
                    </p>
                </div>
            </div>
        </div>
        
        <!-- Navigation Links -->
        <div class="mt-12 text-center">
            <h3 class="text-xl font-bold mb-6">
                <?php echo $lang === 'ar' ? 'اختبر الصفحات الأخرى:' : 'Test Other Pages:'; ?>
            </h3>
            
            <div class="flex flex-wrap justify-center gap-4">
                <a href="index.php?lang=<?php echo $lang; ?>" class="bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600 transition-colors">
                    <?php echo $lang === 'ar' ? 'الصفحة الرئيسية' : 'Homepage'; ?>
                </a>
                <a href="about.php?lang=<?php echo $lang; ?>" class="bg-green-500 text-white px-6 py-3 rounded-lg hover:bg-green-600 transition-colors">
                    <?php echo $lang === 'ar' ? 'من نحن' : 'About Us'; ?>
                </a>
                <a href="blog.php?lang=<?php echo $lang; ?>" class="bg-purple-500 text-white px-6 py-3 rounded-lg hover:bg-purple-600 transition-colors">
                    <?php echo $lang === 'ar' ? 'المدونة' : 'Blog'; ?>
                </a>
                <a href="contact.php?lang=<?php echo $lang; ?>" class="bg-red-500 text-white px-6 py-3 rounded-lg hover:bg-red-600 transition-colors">
                    <?php echo $lang === 'ar' ? 'اتصل بنا' : 'Contact'; ?>
                </a>
                <a href="gallery.php?lang=<?php echo $lang; ?>" class="bg-yellow-500 text-white px-6 py-3 rounded-lg hover:bg-yellow-600 transition-colors">
                    <?php echo $lang === 'ar' ? 'المعرض' : 'Gallery'; ?>
                </a>
            </div>
        </div>
        
        <!-- Language Switch -->
        <div class="mt-8 text-center">
            <div class="inline-flex gap-2">
                <a href="?lang=en" class="px-4 py-2 bg-gray-200 rounded <?php echo $lang === 'en' ? 'bg-blue-500 text-white' : ''; ?>">
                    English
                </a>
                <a href="?lang=ar" class="px-4 py-2 bg-gray-200 rounded <?php echo $lang === 'ar' ? 'bg-blue-500 text-white' : ''; ?>">
                    العربية
                </a>
            </div>
        </div>
        
        <!-- Success Message -->
        <div class="mt-12 bg-green-100 border border-green-400 text-green-700 px-6 py-4 rounded-lg">
            <div class="flex items-center">
                <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <div>
                    <h4 class="font-bold">
                        <?php echo $lang === 'ar' ? 'تم الإصلاح بنجاح!' : 'Successfully Fixed!'; ?>
                    </h4>
                    <p class="text-sm">
                        <?php echo $lang === 'ar' ? 
                            'إذا كنت تستطيع رؤية هذه الرسالة والمحتوى أعلاه، فإن مشكلة الشفافية قد تم حلها.' : 
                            'If you can see this message and the content above, the opacity issue has been resolved.'; ?>
                    </p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Custom JavaScript -->
    <script src="assets/js/main.js"></script>
    
    <script>
        console.log('✅ Test page loaded successfully');
        console.log('✅ All content should be visible');
        
        // Verify elements are visible
        document.addEventListener('DOMContentLoaded', function() {
            const elements = document.querySelectorAll('.scroll-animate, .article-card, .destination-card, .gallery-item');
            let visibleCount = 0;
            
            elements.forEach(element => {
                const style = window.getComputedStyle(element);
                if (style.opacity === '1' && style.visibility === 'visible') {
                    visibleCount++;
                }
            });
            
            console.log(`✅ ${visibleCount}/${elements.length} elements are visible`);
            
            if (visibleCount === elements.length) {
                console.log('🎉 ALL ELEMENTS ARE VISIBLE - FIX SUCCESSFUL!');
            } else {
                console.log('⚠️ Some elements may still be hidden');
            }
        });
    </script>
</body>
</html>
