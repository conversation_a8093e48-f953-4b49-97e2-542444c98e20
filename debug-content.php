<?php
session_start();
require_once 'includes/config.php';

$lang = $_SESSION['lang'] ?? DEFAULT_LANG;
$isRTL = $lang === 'ar';
?>

<!DOCTYPE html>
<html lang="<?php echo $lang; ?>" dir="<?php echo $isRTL ? 'rtl' : 'ltr'; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Content Debug</title>
    
    <!-- Fonts -->
    <?php if ($lang === 'ar'): ?>
        <link href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <?php else: ?>
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <?php endif; ?>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <style>
        .debug-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            background: white;
        }
        
        .visible-content {
            opacity: 1 !important;
            transform: translateY(0) !important;
            transition: none !important;
        }
        
        .font-arabic {
            font-family: 'IBM Plex Sans Arabic', sans-serif;
        }
        
        .font-english {
            font-family: 'Inter', sans-serif;
        }
    </style>
</head>
<body class="<?php echo $lang === 'ar' ? 'font-arabic' : 'font-english'; ?> bg-gray-50 p-8">
    
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-8 text-center">
            <?php echo $lang === 'ar' ? 'اختبار المحتوى' : 'Content Debug Test'; ?>
        </h1>
        
        <!-- Test 1: Basic Content -->
        <div class="debug-section">
            <h2 class="text-2xl font-bold mb-4 text-blue-600">
                <?php echo $lang === 'ar' ? 'اختبار 1: المحتوى الأساسي' : 'Test 1: Basic Content'; ?>
            </h2>
            <p class="text-gray-700 mb-4">
                <?php echo $lang === 'ar' ? 
                    'هذا نص تجريبي باللغة العربية. يجب أن يظهر هذا النص بوضوح تام دون أي مشاكل في الشفافية أو الموضع.' : 
                    'This is test content in English. This text should appear clearly without any opacity or positioning issues.'; ?>
            </p>
            <div class="bg-green-100 p-4 rounded">
                <strong><?php echo $lang === 'ar' ? 'حالة:' : 'Status:'; ?></strong> 
                <?php echo $lang === 'ar' ? 'مرئي' : 'Visible'; ?>
            </div>
        </div>
        
        <!-- Test 2: Content with scroll-animate class -->
        <div class="debug-section scroll-animate">
            <h2 class="text-2xl font-bold mb-4 text-purple-600">
                <?php echo $lang === 'ar' ? 'اختبار 2: محتوى مع فئة الرسوم المتحركة' : 'Test 2: Content with scroll-animate class'; ?>
            </h2>
            <p class="text-gray-700 mb-4">
                <?php echo $lang === 'ar' ? 
                    'هذا المحتوى يحتوي على فئة scroll-animate. إذا كان مرئياً، فإن الإصلاحات تعمل بشكل صحيح.' : 
                    'This content has the scroll-animate class. If it\'s visible, the fixes are working correctly.'; ?>
            </p>
            <div class="bg-blue-100 p-4 rounded">
                <strong><?php echo $lang === 'ar' ? 'فئة CSS:' : 'CSS Class:'; ?></strong> scroll-animate
            </div>
        </div>
        
        <!-- Test 3: Multiple animated elements -->
        <div class="debug-section">
            <h2 class="text-2xl font-bold mb-6 text-red-600">
                <?php echo $lang === 'ar' ? 'اختبار 3: عناصر متعددة متحركة' : 'Test 3: Multiple Animated Elements'; ?>
            </h2>
            
            <div class="grid md:grid-cols-3 gap-4">
                <div class="scroll-animate bg-yellow-100 p-4 rounded">
                    <h3 class="font-bold mb-2"><?php echo $lang === 'ar' ? 'عنصر 1' : 'Element 1'; ?></h3>
                    <p class="text-sm"><?php echo $lang === 'ar' ? 'محتوى تجريبي' : 'Test content'; ?></p>
                </div>
                
                <div class="scroll-animate bg-green-100 p-4 rounded">
                    <h3 class="font-bold mb-2"><?php echo $lang === 'ar' ? 'عنصر 2' : 'Element 2'; ?></h3>
                    <p class="text-sm"><?php echo $lang === 'ar' ? 'محتوى تجريبي' : 'Test content'; ?></p>
                </div>
                
                <div class="scroll-animate bg-blue-100 p-4 rounded">
                    <h3 class="font-bold mb-2"><?php echo $lang === 'ar' ? 'عنصر 3' : 'Element 3'; ?></h3>
                    <p class="text-sm"><?php echo $lang === 'ar' ? 'محتوى تجريبي' : 'Test content'; ?></p>
                </div>
            </div>
        </div>
        
        <!-- Test 4: Counter elements -->
        <div class="debug-section">
            <h2 class="text-2xl font-bold mb-6 text-green-600">
                <?php echo $lang === 'ar' ? 'اختبار 4: العدادات' : 'Test 4: Counters'; ?>
            </h2>
            
            <div class="grid md:grid-cols-4 gap-4 text-center">
                <div class="scroll-animate bg-gray-100 p-4 rounded">
                    <div class="text-3xl font-bold text-blue-600 counter" data-target="100">100</div>
                    <div class="text-sm"><?php echo $lang === 'ar' ? 'عملاء' : 'Clients'; ?></div>
                </div>
                
                <div class="scroll-animate bg-gray-100 p-4 rounded">
                    <div class="text-3xl font-bold text-green-600 counter" data-target="50">50</div>
                    <div class="text-sm"><?php echo $lang === 'ar' ? 'مشاريع' : 'Projects'; ?></div>
                </div>
                
                <div class="scroll-animate bg-gray-100 p-4 rounded">
                    <div class="text-3xl font-bold text-purple-600 counter" data-target="25">25</div>
                    <div class="text-sm"><?php echo $lang === 'ar' ? 'جوائز' : 'Awards'; ?></div>
                </div>
                
                <div class="scroll-animate bg-gray-100 p-4 rounded">
                    <div class="text-3xl font-bold text-red-600 counter" data-target="5">5</div>
                    <div class="text-sm"><?php echo $lang === 'ar' ? 'سنوات' : 'Years'; ?></div>
                </div>
            </div>
        </div>
        
        <!-- Debug Information -->
        <div class="debug-section bg-yellow-50 border-yellow-300">
            <h2 class="text-2xl font-bold mb-4 text-yellow-800">
                <?php echo $lang === 'ar' ? 'معلومات التشخيص' : 'Debug Information'; ?>
            </h2>
            
            <div class="space-y-2 text-sm">
                <p><strong><?php echo $lang === 'ar' ? 'اللغة الحالية:' : 'Current Language:'; ?></strong> <?php echo $lang; ?></p>
                <p><strong><?php echo $lang === 'ar' ? 'اتجاه النص:' : 'Text Direction:'; ?></strong> <?php echo $isRTL ? 'RTL' : 'LTR'; ?></p>
                <p><strong><?php echo $lang === 'ar' ? 'عدد العناصر المتحركة:' : 'Animated Elements Count:'; ?></strong> <span id="animate-count">-</span></p>
                <p><strong><?php echo $lang === 'ar' ? 'حالة GSAP:' : 'GSAP Status:'; ?></strong> <span id="gsap-status">-</span></p>
            </div>
        </div>
        
        <!-- Navigation Links -->
        <div class="debug-section bg-blue-50 border-blue-300">
            <h2 class="text-2xl font-bold mb-4 text-blue-800">
                <?php echo $lang === 'ar' ? 'روابط التنقل' : 'Navigation Links'; ?>
            </h2>
            
            <div class="flex flex-wrap gap-4">
                <a href="index.php?lang=<?php echo $lang; ?>" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                    <?php echo $lang === 'ar' ? 'الصفحة الرئيسية' : 'Homepage'; ?>
                </a>
                <a href="about.php?lang=<?php echo $lang; ?>" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                    <?php echo $lang === 'ar' ? 'من نحن' : 'About Us'; ?>
                </a>
                <a href="blog.php?lang=<?php echo $lang; ?>" class="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600">
                    <?php echo $lang === 'ar' ? 'المدونة' : 'Blog'; ?>
                </a>
                <a href="contact.php?lang=<?php echo $lang; ?>" class="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600">
                    <?php echo $lang === 'ar' ? 'اتصل بنا' : 'Contact'; ?>
                </a>
                <a href="test-animations.php?lang=<?php echo $lang; ?>" class="bg-yellow-500 text-white px-4 py-2 rounded hover:bg-yellow-600">
                    <?php echo $lang === 'ar' ? 'اختبار الرسوم المتحركة' : 'Animation Test'; ?>
                </a>
            </div>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Force all content to be visible
            const animatedElements = document.querySelectorAll('.scroll-animate');
            animatedElements.forEach(element => {
                element.style.opacity = '1';
                element.style.transform = 'translateY(0)';
                element.style.transition = 'all 0.3s ease';
            });
            
            // Update debug info
            document.getElementById('animate-count').textContent = animatedElements.length;
            document.getElementById('gsap-status').textContent = typeof gsap !== 'undefined' ? 'Loaded' : 'Not Loaded';
            
            console.log('Debug: Found', animatedElements.length, 'animated elements');
            console.log('Debug: GSAP available:', typeof gsap !== 'undefined');
        });
    </script>
</body>
</html>
